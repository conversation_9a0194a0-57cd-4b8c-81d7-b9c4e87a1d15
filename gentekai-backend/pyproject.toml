[project]
name = "gentekai"
version = "0.1.0"
description = "GenTekai multi-agent platform"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "alembic~=1.15.2",
    "annotated-types==0.7.0",
    "anyio==4.9.0",
    "asyncpg==0.30.0",
    "attrs==25.3.0",
    "boto3~=1.38.22",
    "botocore~=1.38.22",
    "certifi==2025.4.26",
    "click==8.2.0",
    "deprecated==1.2.18",
    "fastapi==0.115.12",
    "greenlet==3.2.2",
    "h11==0.16.0",
    "httpcore==1.0.9",
    "httpx==0.28.1",
    "idna==3.10",
    "logfire~=3.16.0",
    "numpy~=2.2.6",
    "openai~=1.79.0",
    "openpyxl~=3.1.5",
    "pandas~=2.3.0",
    "pgvector~=0.4.1",
    "pillow~=11.2.1",
    "psycopg==3.2.9",
    "psycopg2-binary==2.9.10",
    "pydantic==2.11.4",
    "pydantic-ai>=0.2.18",
    "pydantic-core==2.33.2",
    "pydantic-settings~=2.9.1",
    "pymupdf~=1.26.0",
    "pytesseract~=0.3.13",
    "python-dateutil==2.9.0.post0",
    "python-docx~=1.1.2",
    "python-jose~=3.4.0",
    "six==1.17.0",
    "slack-sdk>=3.35.0",
    "sniffio==1.3.1",
    "sqlalchemy~=2.0.41",
    "starlette==0.46.2",
    "svix==1.65.0",
    "types-deprecated==1.2.15.20250304",
    "types-python-dateutil==2.9.0.20250516",
    "typing-extensions==4.13.2",
    "typing-inspection==0.4.0",
    "uvicorn==0.34.2",
    "wrapt==1.17.2",
    "docxtpl==0.16.7"
]

[dependency-groups]
dev = [
    "pytest>=8.4.0",
    "pytest-asyncio>=1.0.0",
    "pytest-mock>=3.14.1",
    "ruff>=0.11.13",
]

[tool.uv]
package = true
# packages = ["gentekai"]

[tool.setuptools.packages.find]
include = ["gentekai"]

#[build-system]
#requires = ["poetry-core>=1.0.0"]
#build-backend = "poetry.core.masonry.api"

[tool.ruff]
lint.extend-select = ["I"]

[tool.pytest.ini_options]
addopts = "--capture=no --tb=native"
testpaths = ["tests"]
asyncio_mode = "auto"


