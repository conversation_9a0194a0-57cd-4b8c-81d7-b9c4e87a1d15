services:
  backend:
    container_name: gtk_backend_prod
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - 9878:8000
    env_file:
      - ./.env.prod.secrets
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - gentekai-network

networks:
  gentekai-network:
    external: true
