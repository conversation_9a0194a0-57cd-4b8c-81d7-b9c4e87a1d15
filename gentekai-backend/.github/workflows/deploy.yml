name: CI/CD Pipeline

on:
  push:
    branches:
      - main
      - develop
  workflow_dispatch:

env:
  PROJECT_NAME: gentekai-backend

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-24.04
    container: python:3.13.3
    timeout-minutes: 10
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v6
        with:
          version: ">=0.6,<0.7"
          enable-cache: true
          cache-dependency-glob: "**/uv.lock"
      - name: Install dependencies
        if: steps.setup-uv.outputs.cache-hit == 'true'
        run: uv sync
      # - name: Run linters
      #   run: uv run ruff check .
      # - name: Run tests
      #   run: uv run pytest
        env:
          CI: true

  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create .env.secrets file
        if: github.ref == 'refs/heads/develop'
        run: |
          cat > .env.secrets << EOF
          DATABASE_URL=${{ secrets.DATABASE_URL }}
          CLERK_WEBHOOK_SECRET=${{ secrets.CLERK_WEBHOOK_SECRET }}
          OPENROUTER_API_KEY=${{ secrets.OPENROUTER_API_KEY }}
          CLERK_ISSUER=${{ secrets.CLERK_ISSUER }}
          JWKS_PUBLIC_KEY=${{ secrets.JWKS_PUBLIC_KEY }}
          CLERK_PUBLISHABLE_KEY=${{ secrets.CLERK_PUBLISHABLE_KEY }}
          UPLOAD_ENDPOINT_URL=${{ secrets.UPLOAD_ENDPOINT_URL }}
          UPLOAD_ACCESS_KEY_ID=${{ secrets.UPLOAD_ACCESS_KEY_ID }}
          UPLOAD_ACCESS_KEY=${{ secrets.UPLOAD_ACCESS_KEY }}
          UPLOAD_BUCKET_NAME=${{ secrets.UPLOAD_BUCKET_NAME }}
          UPLOAD_PUBLIC_URL=${{ secrets.UPLOAD_PUBLIC_URL }}
          OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
          SLACK_BOT_TOKEN=${{ secrets.SLACK_BOT_TOKEN }}
          SLACK_SIGNING_SECRET=${{ secrets.SLACK_SIGNING_SECRET }}
          SLACK_BOT_ID=${{ secrets.SLACK_BOT_ID }}
          SQL_TOOL_URL=${{ secrets.SQL_TOOL_URL }}
          EOF

      - name: Create .env.prod.secrets file
        if: github.ref == 'refs/heads/main'
        run: |
          cat > .env.prod.secrets << EOF
          DATABASE_URL=${{ secrets.DATABASE_URL_PROD }}
          CLERK_WEBHOOK_SECRET=${{ secrets.CLERK_WEBHOOK_SECRET }}
          OPENROUTER_API_KEY=${{ secrets.OPENROUTER_API_KEY_PROD }}
          CLERK_ISSUER=${{ secrets.CLERK_ISSUER }}
          JWKS_PUBLIC_KEY=${{ secrets.JWKS_PUBLIC_KEY }}
          CLERK_PUBLISHABLE_KEY=${{ secrets.CLERK_PUBLISHABLE_KEY }}
          UPLOAD_ENDPOINT_URL=${{ secrets.UPLOAD_ENDPOINT_URL_PROD }}
          UPLOAD_ACCESS_KEY_ID=${{ secrets.UPLOAD_ACCESS_KEY_ID }}
          UPLOAD_ACCESS_KEY=${{ secrets.UPLOAD_ACCESS_KEY }}
          UPLOAD_BUCKET_NAME=${{ secrets.UPLOAD_BUCKET_NAME_PROD }}
          UPLOAD_PUBLIC_URL=${{ secrets.UPLOAD_PUBLIC_URL_PROD }}
          OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY_PROD }}
          SLACK_BOT_TOKEN=${{ secrets.SLACK_BOT_TOKEN_PROD }}
          SLACK_SIGNING_SECRET=${{ secrets.SLACK_SIGNING_SECRET_PROD }}
          SLACK_BOT_ID=${{ secrets.SLACK_BOT_ID_PROD }}
          SQL_TOOL_URL=${{ secrets.SQL_TOOL_URL_PROD }}
          EOF

      - name: Set environment variables based on branch
        run: |
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "DOCKER_COMPOSE_FILE=docker-compose.prod.yml" >> $GITHUB_ENV
            echo "HEALTH_CHECK_URL=http://localhost:9878/api/health" >> $GITHUB_ENV
            echo "PROJECT_PREFIX=${{ env.PROJECT_NAME }}-prod" >> $GITHUB_ENV
            echo "ENV_SECRETS_FILE=.env.prod.secrets" >> $GITHUB_ENV
          else
            echo "DOCKER_COMPOSE_FILE=docker-compose.dev.yml" >> $GITHUB_ENV
            echo "HEALTH_CHECK_URL=http://localhost:9876/api/health" >> $GITHUB_ENV
            echo "PROJECT_PREFIX=${{ env.PROJECT_NAME }}-dev" >> $GITHUB_ENV
            echo "ENV_SECRETS_FILE=.env.secrets" >> $GITHUB_ENV
          fi

      - name: Build Docker image
        run: |
          docker build -t ${{ env.PROJECT_NAME }}:${{ github.sha }} -f Dockerfile.dev .
          docker save ${{ env.PROJECT_NAME }}:${{ github.sha }} > /tmp/image.tar

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add server to known hosts
        run: |
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts
          chmod 600 ~/.ssh/known_hosts

      - name: Deploy to server
        run: |
          # Transfer files
          scp /tmp/image.tar ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/${{ env.PROJECT_NAME }}/
          scp ${{ env.DOCKER_COMPOSE_FILE }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/${{ env.PROJECT_NAME }}/
          scp ${{ env.ENV_SECRETS_FILE }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/${{ env.PROJECT_NAME }}/

          # Deploy on server
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            set -e
            
            cd ~/${{ env.PROJECT_NAME }}

            echo 'Saving current deployment info...'
            docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} ps || true

            if docker image inspect ${{ env.PROJECT_NAME }}:latest >/dev/null 2>&1; then
              echo 'Backing up current image...'
              docker tag ${{ env.PROJECT_NAME }}:latest ${{ env.PROJECT_NAME }}:previous
            else
              echo 'No existing image to backup'
            fi

            # Load new image
            echo 'Loading new image...'
            docker load -i image.tar
            docker tag ${{ env.PROJECT_NAME }}:${{ github.sha }} ${{ env.PROJECT_NAME }}:latest

            # Deploy
            echo 'Stopping current containers...'
            docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} down --timeout 30

            echo 'Starting new deployment...'
            docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} up -d

            echo 'Waiting for services to start...'
            sleep 30

            # Health check
            echo 'Performing health check...'
            HEALTH_CHECK_PASSED=false
            for i in {1..12}; do
              if curl -f --max-time 5 ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
                echo '✅ Health check passed!'
                HEALTH_CHECK_PASSED=true
                break
              fi
              echo \"Health check attempt \$i/12 failed, waiting...\"
              sleep 15
            done

            # Rollback if health check failed
            if [ \"\$HEALTH_CHECK_PASSED\" = false ]; then
              echo '❌ Health check failed! Initiating rollback...'
              
              # Stop failed deployment
              docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} down --timeout 30
              
              # Check if previous image exists for rollback
              if docker image inspect ${{ env.PROJECT_NAME }}:previous >/dev/null 2>&1; then
                echo 'Rolling back to previous image...'
                docker tag ${{ env.PROJECT_NAME }}:previous ${{ env.PROJECT_NAME }}:latest
                docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} up -d
                
                # Verify rollback
                sleep 30
                for i in {1..6}; do
                  if curl -f --max-time 5 ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
                    echo '✅ Rollback successful!'
                    break
                  fi
                  echo \"Rollback verification attempt \$i/6...\"
                  sleep 10
                done
              else
                echo '⚠️ No previous image available for rollback!'
              fi
              
              echo '❌ Deployment failed and rollback completed'
              exit 1
            fi

            # Final verification
            echo 'Final deployment verification...'
            docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} ps
            
            # Test one more time to be sure
            if curl -f --max-time 10 ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
              echo '✅ Deployment successful and verified!'
              
              # Cleanup old images (keep only latest and previous)
              echo 'Cleaning up old images...'
              docker images ${{ env.PROJECT_NAME }} --format '{{.Repository}}:{{.Tag}}' | grep -v ':latest\|:previous' | xargs -r docker rmi || true
              
              echo 'Current images:'
              docker images ${{ env.PROJECT_NAME }} --format 'table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}\t{{.Size}}'
            else
              echo '❌ Final verification failed!'
              exit 1
            fi

            # Cleanup
            rm -f image.tar
          "
