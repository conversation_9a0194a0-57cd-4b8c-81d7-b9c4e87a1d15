#!/usr/bin/env python3
"""
Test suite for document generation service.
Tests template generation and document creation functionality.
"""

import sys
import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock
import tempfile
import shutil
import os

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from gentekai.services.document_generation_service import DocumentGenerationService


class TestDocumentGenerationService:
    """Test class for DocumentGenerationService."""
    
    @pytest.fixture
    def doc_service(self):
        """Fixture to create a DocumentGenerationService instance."""
        return DocumentGenerationService()
    
    @pytest.fixture
    def sample_cv_data(self):
        """Fixture to provide sample CV data for testing."""
        return {
            "personal_info": {
                "name": "<PERSON>",
                "job_title": "Senior Software Engineer"
            },
            "about_me": "Experienced software engineer with 5+ years of experience in full-stack development, specializing in Python, JavaScript, and cloud technologies.",
            "key_skills": [
                {
                    "type": "Programming Languages",
                    "list": ["Python", "JavaScript", "TypeScript", "Java"]
                },
                {
                    "type": "Frameworks & Libraries",
                    "list": ["React", "Django", "FastAPI", "Spring Boot"]
                },
                {
                    "type": "Databases",
                    "list": ["PostgreSQL", "MongoDB", "Redis"]
                },
                {
                    "type": "Cloud & DevOps",
                    "list": ["AWS", "Docker", "Kubernetes", "Jenkins", "GitHub Actions"]
                },
                {
                    "type": "Testing & Tools",
                    "list": ["Pytest", "Jest", "Selenium", "Git", "Jira"]
                }
            ],
            "education": {
                "degree": "Bachelor of Science in Computer Science",
                "school": "Stanford University"
            },
            "work_experiences": [
                {
                    "start_date": "Jan 2022",
                    "end_date": "Present",
                    "position": "Senior Software Engineer",
                    "company": "TechCorp Inc.",
                    "location": "San Francisco, CA",
                    "description": "Leading development of scalable microservices architecture and mentoring junior developers."
                },
                {
                    "start_date": "Mar 2020",
                    "end_date": "Dec 2021",
                    "position": "Full Stack Developer",
                    "company": "StartupXYZ",
                    "location": "New York, NY",
                    "description": "Built and maintained web applications using modern technologies."
                }
            ],
            "projects": [
                {
                    "name": "E-commerce Platform",
                    "duration": "6 months",
                    "description": "Built a scalable e-commerce platform serving 100k+ users.",
                    "main_stacks": ["React", "Node.js", "PostgreSQL", "AWS", "Redis"],
                    "responsibilities": [
                        "Led frontend development team of 4 developers",
                        "Implemented microservices architecture",
                        "Optimized database queries reducing load times by 60%",
                        "Deployed to AWS using CI/CD pipeline",
                        "Integrated payment gateways and inventory systems"
                    ]
                },
                {
                    "name": "AI-Powered Analytics Dashboard",
                    "duration": "3 months",
                    "description": "Created an intelligent analytics dashboard using machine learning.",
                    "main_stacks": ["Vue.js", "Python", "TensorFlow", "PostgreSQL", "Kubernetes"],
                    "responsibilities": [
                        "Implemented machine learning models for data analysis",
                        "Built interactive data visualization components",
                        "Designed RESTful APIs for data processing",
                        "Deployed ML models using Kubernetes",
                        "Created automated reporting system"
                    ]
                }
            ]
        }
    
    def test_service_initialization(self, doc_service):
        """Test that DocumentGenerationService initializes correctly."""
        assert doc_service is not None
        assert hasattr(doc_service, 'template_dir')
        assert doc_service.template_dir.exists()
    
    def test_template_directory_exists(self, doc_service):
        """Test that the template directory exists and contains expected files."""
        template_dir = doc_service.template_dir
        assert template_dir.exists()
        assert template_dir.is_dir()
        
        # Check if cv_template.docx exists
        cv_template = template_dir / "cv_template.docx"
        assert cv_template.exists(), f"CV template not found at {cv_template}"
    
    def test_generate_cv_document_success(self, doc_service, sample_cv_data):
        """Test successful CV document generation."""
        try:
            print(f"\n📁 Temporary directory: {tempfile.gettempdir()}")
            file_path = doc_service.generate_cv_document(
                cv_data=sample_cv_data,
                template_name="cv_template.docx",
                output_filename="test_cv_success.docx"
            )
            
            print(f"📄 Generated file path: {file_path}")
            
            # Verify file was created
            file_path_obj = Path(file_path)
            assert file_path_obj.exists(), f"Generated file not found at {file_path}"
            
            # Verify file has content
            file_size = file_path_obj.stat().st_size
            print(f"📊 File size: {file_size} bytes")
            assert file_size > 0, f"Generated file is empty (size: {file_size} bytes)"
            
            # Clean up
            file_path_obj.unlink()
            print("🧹 Test file cleaned up")
            
        except Exception as e:
            pytest.fail(f"Document generation failed: {e}")
    
    def test_generate_cv_document_with_custom_filename(self, doc_service, sample_cv_data):
        """Test CV document generation with custom filename."""
        custom_filename = "custom_cv_test.docx"
        
        print(f"\n📁 Temporary directory: {tempfile.gettempdir()}")
        file_path = doc_service.generate_cv_document(
            cv_data=sample_cv_data,
            output_filename=custom_filename
        )
        
        print(f"📄 Generated file path: {file_path}")
        
        # Verify custom filename was used
        assert custom_filename in file_path
        
        # Clean up
        Path(file_path).unlink()
        print("🧹 Test file cleaned up")
    
    def test_generate_cv_document_auto_filename(self, doc_service, sample_cv_data):
        """Test CV document generation with automatic filename generation."""
        print(f"\n📁 Temporary directory: {tempfile.gettempdir()}")
        file_path = doc_service.generate_cv_document(
            cv_data=sample_cv_data
        )
        
        print(f"📄 Generated file path: {file_path}")
        
        # Verify filename contains the person's name
        assert "John_Doe" in file_path
        assert file_path.endswith("_generated.docx")
        
        # Clean up
        Path(file_path).unlink()
        print("🧹 Test file cleaned up")
    
    def test_generate_cv_document_template_not_found(self, doc_service, sample_cv_data):
        """Test that appropriate error is raised when template is not found."""
        with pytest.raises(FileNotFoundError):
            doc_service.generate_cv_document(
                cv_data=sample_cv_data,
                template_name="nonexistent_template.docx"
            )
    
    def test_generate_cv_document_with_empty_data(self, doc_service):
        """Test CV document generation with empty data."""
        empty_data = {
            "personal_info": {
                "name": "Test User",
                "job_title": ""
            },
            "about_me": "",
            "key_skills": [
                {
                    "type": "",
                    "list": []
                }
            ],
            "education": {
                "degree": "",
                "school": ""
            },
            "work_experiences": [],
            "projects": []
        }
        
        try:
            print(f"\n📁 Temporary directory: {tempfile.gettempdir()}")
            file_path = doc_service.generate_cv_document(
                cv_data=empty_data,
                output_filename="test_empty_cv.docx"
            )
            
            print(f"📄 Generated file path: {file_path}")
            
            # Should still generate a file
            assert Path(file_path).exists()
            
            # Clean up
            Path(file_path).unlink()
            print("🧹 Test file cleaned up")
            
        except Exception as e:
            pytest.fail(f"Document generation with empty data failed: {e}")
    
    def test_generate_cv_document_with_missing_personal_info(self, doc_service):
        """Test CV document generation with missing personal info."""
        incomplete_data = {
            "personal_info": {
                "name": "",
                "job_title": ""
            },
            "about_me": "Test about me",
            "key_skills": [
                {
                    "type": "",
                    "list": []
                }
            ],
            "education": {
                "degree": "",
                "school": ""
            },
            "work_experiences": [],
            "projects": []
        }
        
        try:
            print(f"\n📁 Temporary directory: {tempfile.gettempdir()}")
            file_path = doc_service.generate_cv_document(
                cv_data=incomplete_data,
                output_filename="test_incomplete_cv.docx"
            )
            
            print(f"📄 Generated file path: {file_path}")
            
            # Should still generate a file
            assert Path(file_path).exists()
            
            # Clean up
            Path(file_path).unlink()
            print("🧹 Test file cleaned up")
            
        except Exception as e:
            pytest.fail(f"Document generation with incomplete data failed: {e}")
    
    @patch('tempfile.gettempdir')
    def test_generate_cv_document_temp_directory(self, mock_tempdir, doc_service, sample_cv_data):
        """Test that document is generated in the correct temporary directory."""
        import os
        mock_tempdir.return_value = "/tmp/test_temp"
        
        # Create the mock directory if it doesn't exist
        os.makedirs("/tmp/test_temp", exist_ok=True)
        
        print(f"\n📁 Mock temporary directory: /tmp/test_temp")
        file_path = doc_service.generate_cv_document(
            cv_data=sample_cv_data,
            output_filename="test_temp_dir.docx"
        )
        
        print(f"📄 Generated file path: {file_path}")
        assert "/tmp/test_temp" in file_path
        
        # Clean up if file was actually created
        if Path(file_path).exists():
            Path(file_path).unlink()
            print("🧹 Test file cleaned up")
    
    def test_generate_cv_document_with_special_characters(self, doc_service):
        """Test CV document generation with special characters in name."""
        special_char_data = {
            "personal_info": {
                "name": "José María O'Connor-Smith",
                "job_title": "Software Engineer"
            },
            "about_me": "Test about me",
            "key_skills": [
                {
                    "type": "",
                    "list": []
                }
            ],
            "education": {
                "degree": "",
                "school": ""
            },
            "work_experiences": [],
            "projects": []
        }
        
        try:
            print(f"\n📁 Temporary directory: {tempfile.gettempdir()}")
            file_path = doc_service.generate_cv_document(
                cv_data=special_char_data,
                output_filename="test_special_chars.docx"
            )
            
            print(f"📄 Generated file path: {file_path}")
            
            # Should generate a file
            assert Path(file_path).exists()
            
            # Clean up
            Path(file_path).unlink()
            print("🧹 Test file cleaned up")
            
        except Exception as e:
            pytest.fail(f"Document generation with special characters failed: {e}")

    def test_generate_cv_document_for_inspection(self, doc_service, sample_cv_data):
        """Test CV document generation and keep file for inspection.
        
        This test generates a file and keeps it around so you can inspect it.
        The file will be in the system's temporary directory.
        """
        print(f"\n🔍 GENERATING FILE FOR INSPECTION")
        print(f"📁 Temporary directory: {tempfile.gettempdir()}")
        print(f"💡 You can find the generated file in the temporary directory above")
        
        file_path = doc_service.generate_cv_document(
            cv_data=sample_cv_data,
            output_filename="INSPECT_THIS_FILE.docx"
        )
        
        print(f"📄 Generated file path: {file_path}")
        
        # Verify file was created
        file_path_obj = Path(file_path)
        assert file_path_obj.exists(), f"Generated file not found at {file_path}"
        
        # Verify file has content
        file_size = file_path_obj.stat().st_size
        print(f"📊 File size: {file_size} bytes")
        assert file_size > 0, f"Generated file is empty (size: {file_size} bytes)"
        
        print(f"✅ File generated successfully!")
        print(f"🔍 You can now open and inspect: {file_path}")
        print(f"⚠️  This file will NOT be automatically cleaned up")
        print(f"🧹 To clean up manually, delete: {file_path}")
        
        # Don't clean up - let the user inspect the file
        # Path(file_path).unlink()  # Commented out for inspection
        
        return file_path  # Return the path for potential further use


def run_integration_test():
    """Run integration test for document generation."""
    print("\n🧪 Running Document Generation Integration Test")
    print("=" * 60)
    
    # Initialize service
    doc_service = DocumentGenerationService()
    
    # Sample data for integration test
    test_data = {
        "personal_info": {
            "name": "Integration Test User",
            "job_title": "Senior Software Engineer"
        },
        "about_me": "This is an integration test to verify the complete document generation workflow.",
        "key_skills": [
            {
                "type": "Programming Languages",
                "list": ["Python", "JavaScript", "TypeScript"]
            },
            {
                "type": "Frameworks",
                "list": ["React", "Django", "FastAPI"]
            },
            {
                "type": "Databases",
                "list": ["PostgreSQL", "MongoDB"]
            },
            {
                "type": "Tools & Technologies",
                "list": ["Git", "Jira", "AWS", "Docker", "Jenkins", "GitHub Actions", "Pytest", "Jest"]
            }
        ],
        "education": {
            "degree": "Master of Science in Computer Science",
            "school": "Test University"
        },
        "work_experiences": [
            {
                "start_date": "Jan 2022",
                "end_date": "Present",
                "position": "Senior Software Engineer",
                "company": "Integration Test Corp",
                "location": "Test City, TC",
                "description": "Leading development of test applications and mentoring developers."
            }
        ],
        "projects": [
            {
                "name": "Integration Test Platform",
                "duration": "3 months",
                "description": "Built a comprehensive testing platform for integration testing.",
                "main_stacks": ["React", "Python", "PostgreSQL", "Docker"],
                "responsibilities": [
                    "Led development team of 3 developers",
                    "Implemented automated testing framework",
                    "Optimized test execution time by 70%",
                    "Deployed using CI/CD pipeline",
                    "Integrated with multiple testing tools"
                ]
            }
        ]
    }
    
    try:
        # Test 1: Basic document generation
        print("📄 Testing basic document generation...")
        print(f"📁 Temporary directory: {tempfile.gettempdir()}")
        file_path = doc_service.generate_cv_document(
            cv_data=test_data,
            output_filename="integration_test_cv.docx"
        )
        
        # Verify file was created
        file_path_obj = Path(file_path)
        if file_path_obj.exists():
            file_size = file_path_obj.stat().st_size
            print(f"✅ Document generated successfully!")
            print(f"📁 File path: {file_path}")
            print(f"📊 File size: {file_size} bytes")
            
            if file_size > 0:
                print("✅ File has content!")
                
                # Clean up
                file_path_obj.unlink()
                print("🧹 Test file cleaned up")
                return True
            else:
                print("⚠️ File is empty!")
                return False
        else:
            print("❌ Generated file not found!")
            return False
            
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


if __name__ == "__main__":
    # Run integration test when script is executed directly
    success = run_integration_test()
    
    if success:
        print("\n🎉 Integration test passed! The service is working correctly.")
        print("\n📋 Next steps:")
        print("1. Run pytest for unit tests: pytest tests/test_document_generation.py -v")
        print("2. Check the generated document for proper formatting")
        print("3. Verify that all template variables are replaced correctly")
    else:
        print("\n❌ Integration test failed!")
        sys.exit(1) 