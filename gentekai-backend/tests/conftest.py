"""
Pytest configuration and shared fixtures for Gentekai backend tests.
"""

import sys
import pytest
from pathlib import Path
import tempfile
import shutil

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


@pytest.fixture(scope="session")
def test_data_dir():
    """Fixture to provide a temporary test data directory."""
    temp_dir = Path(tempfile.mkdtemp(prefix="gentekai_test_"))
    yield temp_dir
    # Cleanup after all tests
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture(scope="function")
def temp_file():
    """Fixture to provide a temporary file path."""
    temp_file = Path(tempfile.mktemp(suffix=".docx"))
    yield temp_file
    # Cleanup after each test
    if temp_file.exists():
        temp_file.unlink()


@pytest.fixture(scope="function")
def sample_cv_data():
    """Fixture to provide sample CV data for testing."""
    return {
        "personal_info": {
            "name": "Test User",
            "job_title": "Software Engineer"
        },
        "about_me": "This is a test CV for testing purposes.",
        "key_skills": [
            {
                "type": "Programming Languages",
                "list": ["Python", "JavaScript", "TypeScript", "Java"]
            },
            {
                "type": "Frameworks",
                "list": ["React", "Django", "FastAPI", "Spring Boot"]
            },
            {
                "type": "Databases",
                "list": ["PostgreSQL", "MongoDB", "Redis"]
            },
            {
                "type": "Tools & Technologies",
                "list": ["Git", "Docker", "AWS", "Kubernetes"]
            }
        ],
        "education": {
            "degree": "Bachelor of Science in Computer Science",
            "school": "Test University"
        },
        "work_experiences": [
            {
                "start_date": "Jan 2021",
                "end_date": "Present",
                "position": "Software Engineer",
                "company": "Test Corp",
                "location": "Test City, TC",
                "description": "Test work experience description."
            }
        ],
        "projects": [
            {
                "name": "Test Project",
                "duration": "3 months",
                "description": "A test project for testing purposes.",
                "main_stacks": ["Python", "React"],
                "responsibilities": [
                    "Test responsibility 1",
                    "Test responsibility 2"
                ]
            }
        ]
    }


@pytest.fixture(scope="function")
def empty_cv_data():
    """Fixture to provide empty CV data for testing edge cases."""
    return {
        "personal_info": {
            "name": "",
            "job_title": ""
        },
        "about_me": "",
        "key_skills": [
            {
                "type": "",
                "list": []
            }
        ],
        "education": {
            "degree": "",
            "school": ""
        },
        "work_experiences": [
            {
                "start_date": "",
                "end_date": "",
                "position": "",
                "company": "",
                "location": "",
                "description": ""
            }
        ],
        "projects": [
            {
                "name": "",
                "duration": "",
                "description": "",
                "main_stacks": [],
                "responsibilities": []
            }
        ]
    }


@pytest.fixture(scope="function")
def incomplete_cv_data():
    """Fixture to provide incomplete CV data for testing error handling."""
    return {
        "about_me": "Test about me",
        "key_skills": {},
        "education": {},
        "work_experiences": [],
        "projects": []
    } 