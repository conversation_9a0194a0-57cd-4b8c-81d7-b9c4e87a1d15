import logging
from datetime import date, datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

from sqlalchemy import and_, desc, func, select, text, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from gentekai.agent.supervisor import (
    orchestrate_with_supervisor,
    performance_monitor,
    record_agent_performance,
    supervisor_monitor,
)
from gentekai.db.models import Conversation, Message, User
from gentekai.services.llm_service import get_chat_completion_raw

logger = logging.getLogger(__name__)


class ChatService:
    """Service focused on chat functionality and message management"""

    def __init__(self, db: Optional[AsyncSession] = None):
        self.db = db
        self._request_count = 0

    # ============================================================================
    # CORE CHAT FUNCTIONALITY
    # ============================================================================

    async def stream_chat_with_agents(
        self,
        prompt: str,
        enhanced_prompt: str,
        conv_id: Optional[str] = None,
        user: Optional[User] = None,
        model: Optional[str] = None,
        file_keys: Optional[List[str]] = None,
        slack_channel_id: Optional[str] = None,
        slack_thread_ts: Optional[str] = None,
    ) -> AsyncGenerator[str, None]:
        """Enhanced stream chat response from supervised multi-agent system"""
        logger.info("Starting stream chat with agents")
        # Initialize tracking variables
        full_response = ""
        agents_used = []
        start_time = datetime.utcnow()
        success = True
        error_details = None

        try:
            if user and not isinstance(user, str) and "id" in vars(user):
                user_id = str(user.id)
                user_role = getattr(user, "role", "user")
            elif isinstance(user, str):
                user_id = user
                user_role = "user"
            else:
                user_id = ""
                user_role = "user"

            logger.info(
                f"Processing enhanced chat request for user {user_id} ({user_role}) in conversation {conv_id}"
            )

            # 1. Validate conversation exists and user has access
            if conv_id and not await self.conversation_exists(conv_id, user_id):
                error_msg = "Error: Conversation not found or access denied"
                logger.error(error_msg)
                yield error_msg
                return

            # 2. Fetch previous messages for context
            history_messages = await self.get_recent_messages(conv_id, limit=20)
            # Convert to structured history format
            history = [
                {
                    "role": msg.sender,
                    "content": msg.content,
                    "file_keys": (msg.extra or {}).get("file_keys", []),
                }
                for msg in history_messages
            ]

            # Log context analysis
            context_complexity = (
                "simple"
                if len(history) <= 5
                else "moderate"
                if len(history) <= 15
                else "complex"
            )
            logger.info(
                f"📚 Context analysis: {len(history)} messages ({context_complexity} conversation)"
            )

            # 3. Store user message with enhanced metadata
            user_message = await self.store_message(
                conversation_id=conv_id,
                sender="user",
                content=prompt,
                extra={
                    "context_length": len(history),
                    "context_complexity": context_complexity,
                    "user_role": user_role,
                    "timestamp": start_time.isoformat(),
                    "file_keys": file_keys,
                },
            )
            logger.info(f"💾 Stored user message {user_message.id}")

            # 4. Enhanced supervisor orchestration
            supervisor_start = datetime.utcnow()

            try:
                async for chunk in orchestrate_with_supervisor(
                    user, enhanced_prompt, history, model, slack_channel_id, slack_thread_ts
                ):
                    # if chunk == "\n\ndata: [DONE]\n\n":
                    #    break
                    # el
                    if chunk.startswith("Agent:"):
                        # Track but don't show to user
                        agent_name = chunk.replace("Agent:", "").strip()
                        if agent_name not in agents_used:
                            agents_used.append(agent_name)
                        logger.info(f"🤖 Agent engaged: {agent_name}")
                        # Don't yield this to user
                    elif (
                        any(emoji in chunk for emoji in ["🎯", "📝", "🔄", "🎉"])
                        and "Analysis:" in chunk
                    ):
                        # Filter out internal supervisor analysis messages
                        logger.info(f"📊 Supervisor internal: {chunk.strip()}")
                        # Don't yield this to user
                    elif chunk.strip().startswith(
                        ("Task breakdown:", "All ", " stages completed")
                    ):
                        # Filter out coordination messages
                        logger.info(f"📊 Supervisor coordination: {chunk.strip()}")
                        # Don't yield this to user
                    else:
                        # This is actual content for the user
                        full_response += chunk
                        yield chunk

            except Exception as supervisor_error:
                logger.error(f"❌ Supervisor execution failed: {supervisor_error}")
                error_details = str(supervisor_error)
                success = False

                # Attempt graceful fallback
                fallback_response = (
                    "🔄 Supervisor encountered an issue. Attempting fallback...\n\n"
                )
                full_response += fallback_response
                yield fallback_response

                # Try direct general agent as emergency fallback
                try:
                    from gentekai.agent.specialized.general_agent import (
                        handle as general_agent_handle,
                    )

                    async for fallback_chunk in general_agent_handle(
                        user, enhanced_prompt, history, model
                    ):
                        if fallback_chunk != "\n\ndata: [DONE]\n\n":
                            full_response += fallback_chunk
                            yield fallback_chunk

                    agents_used.append("general_agent_fallback")
                    logger.info("✅ Emergency fallback completed successfully")

                except Exception as fallback_error:
                    logger.error(f"💥 Emergency fallback also failed: {fallback_error}")
                    emergency_response = (
                        "I encountered multiple system errors. Please try again later."
                    )
                    full_response += emergency_response
                    yield emergency_response
                    success = False

            # Calculate comprehensive response metrics
            response_time = (datetime.utcnow() - start_time).total_seconds()
            supervisor_time = (datetime.utcnow() - supervisor_start).total_seconds()

            # 5. Record performance for each agent used
            primary_agent = agents_used[0] if agents_used else "unknown"

            for agent_name in agents_used:
                record_agent_performance(
                    agent_name=agent_name,
                    success=success,
                    response_time=response_time / len(agents_used),
                )

            # 6. Store enhanced assistant response with comprehensive metadata
            if full_response.strip():
                response_metadata = {
                    "agents_used": agents_used,
                    "primary_agent": primary_agent,
                    "response_time": response_time,
                    "supervisor_time": supervisor_time,
                    "success": success,
                    "user_role": user_role,
                    "context_complexity": context_complexity,
                    "response_length": len(full_response),
                    "multi_agent": len(agents_used) > 1,
                    "supervisor_coordination": any(
                        indicator in full_response for indicator in ["🔗", "🤝", "🧠"]
                    ),
                    "error_details": error_details,
                    "timestamp": datetime.utcnow().isoformat(),
                }

                assistant_message = await self.store_message(
                    conversation_id=conv_id,
                    sender="assistant",
                    content=full_response.strip(),
                    agent_used=primary_agent,
                    response_time=response_time,
                    extra=response_metadata,
                )

                logger.info(
                    f"💾 Stored enhanced assistant message {assistant_message.id}"
                )
                logger.info(f"📊 Agents used: {agents_used}")
                logger.info(f"⏱️ Response time: {response_time:.2f}s")

            # 7. Comprehensive session analytics logging
            logger.info("🎯 === ENHANCED CHAT SESSION COMPLETE ===")
            logger.info(f"📝 Conversation: {conv_id}")
            logger.info(f"👤 User: {user_id} ({user_role})")
            logger.info(
                f"🤖 Agents: {', '.join(agents_used) if agents_used else 'none'}"
            )
            logger.info(f"📊 Multi-agent workflow: {len(agents_used) > 1}")
            logger.info(f"⏱️ Total time: {response_time:.2f}s")
            logger.info(f"✅ Success: {success}")

            # 8. Optional health check trigger (every 50 requests)
            self._request_count += 1
            if self._request_count % 50 == 0:
                logger.info("🏥 Triggering supervisor health check...")
                try:
                    health_report = await supervisor_monitor.run_health_check(
                        performance_monitor
                    )
                    logger.info(
                        f"🏥 Health report: {health_report.get('status', 'unknown')}"
                    )
                except Exception as health_error:
                    logger.error(f"🏥 Health check failed: {health_error}")

        except Exception as e:
            logger.error(f"💥 Critical chat service error: {e}")
            error_response = f"I encountered a critical system error: {str(e)}"
            yield error_response

            # Record the failure
            record_agent_performance("system", False, 0.0)

        yield "\n\ndata: [DONE]\n\n"

    # ============================================================================
    # MESSAGE MANAGEMENT
    # ============================================================================

    async def store_message(
        self,
        conversation_id: str,
        sender: str,
        content: str,
        agent_used: Optional[str] = None,
        response_time: Optional[float] = None,
        embedding: Optional[List[float]] = None,
        extra: Optional[Dict[str, Any]] = None,  # 🆕 NEW: Rich metadata support
    ) -> Message:
        """Store a message in the database"""

        try:
            message_data = {
                "conversation_id": conversation_id,
                "sender": sender,
                "content": content,
            }
            if self.db is None:
                message = Message(**message_data)
                return message
            if embedding:
                message_data["embedding"] = embedding

            # Note: If you add agent_used, response_time fields to your Message model:
            if agent_used:
                message_data["agent_used"] = agent_used
            if response_time:
                message_data["response_time_ms"] = int(response_time * 1000)

            # 🆕 Handle new metadata field
            if extra:
                # Ensure metadata is JSON serializable
                processed_metadata = self._process_metadata(
                    extra, agent_used, response_time
                )
                message_data["extra"] = processed_metadata

                # Log metadata for debugging (optional)
                logger.debug(f"Storing message with metadata: {processed_metadata}")

            message = Message(**message_data)
            self.db.add(message)
            await self.db.commit()
            await self.db.refresh(message)

            # Update conversation's updated_at timestamp
            await self._update_conversation_timestamp(conversation_id)

            return message

        except Exception as e:
            logger.error(f"Failed to store message: {e}")
            raise

    async def get_recent_messages(
        self, conversation_id: str | None = None, limit: int = 50
    ) -> List[Message]:
        """Get recent messages for a conversation (chronological order)"""
        logger.info(
            f"Fetching recent messages for conversation {conversation_id} with limit {limit}"
        )

        if self.db is None or conversation_id is None:
            return []

        try:
            # Get the most recent messages in desc order, then reverse
            stmt = (
                select(Message)
                .where(Message.conversation_id == conversation_id)
                .order_by(desc(Message.created_at))
                .limit(limit)
            )

            result = await self.db.execute(stmt)
            messages = result.scalars().all()

            # Return in chronological order (oldest first)
            return list(reversed(messages))

        except Exception as e:
            logger.error(f"Failed to get recent messages for {conversation_id}: {e}")
            return []

    async def get_message_by_id(self, message_id: str) -> Optional[Message]:
        """Get a specific message by ID"""

        try:
            if self.db is None:
                return None
            stmt = (
                select(Message)
                .options(selectinload(Message.conversation))
                .where(Message.id == message_id)
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Failed to get message {message_id}: {e}")
            return None

    async def update_message(
        self, message_id: str, content: str, agent_used: Optional[str] = None
    ) -> Optional[Message]:
        """Update a message (for regeneration)"""

        try:
            if self.db is None:
                return None

            update_data = {"content": content, "updated_at": datetime.utcnow()}

            # Note: If you add agent_used field to your Message model:
            # if agent_used:
            #     update_data["agent_used"] = agent_used

            stmt = (
                update(Message)
                .where(Message.id == message_id)
                .values(**update_data)
                .returning(Message)
            )

            result = await self.db.execute(stmt)
            await self.db.commit()
            return result.scalar_one_or_none()

        except Exception as e:
            logger.error(f"Failed to update message {message_id}: {e}")
            return None

    async def get_messages_before(
        self, conversation_id: str, before_message_id: str
    ) -> List[Message]:
        """Get messages before a specific message ID"""
        if self.db is None:
            return []
        try:
            # First get the target message to get its timestamp
            target_stmt = select(Message).where(Message.id == before_message_id)
            target_result = await self.db.execute(target_stmt)
            target_message = target_result.scalar_one_or_none()

            if not target_message:
                return []

            # Get messages before this timestamp
            stmt = (
                select(Message)
                .where(
                    and_(
                        Message.conversation_id == conversation_id,
                        Message.created_at < target_message.created_at,
                    )
                )
                .order_by(Message.created_at)
            )

            result = await self.db.execute(stmt)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Failed to get messages before {before_message_id}: {e}")
            return []

    async def search_messages(
        self, conversation_id: str, query: str, limit: int = 20
    ) -> List[Message]:
        """Search messages in a conversation"""
        if self.db is None:
            return []
        try:
            stmt = (
                select(Message)
                .where(
                    and_(
                        Message.conversation_id == conversation_id,
                        Message.content.ilike(f"%{query}%"),
                    )
                )
                .order_by(desc(Message.created_at))
                .limit(limit)
            )

            result = await self.db.execute(stmt)
            return result.scalars().all()

        except Exception as e:
            logger.error(f"Failed to search messages: {e}")
            return []

    def _process_metadata(
        self,
        metadata: Dict[str, Any],
        agent_used: Optional[str] = None,
        response_time: Optional[float] = None,
    ) -> Dict[str, Any]:
        """Process and enhance metadata before storing"""

        processed = metadata.copy()

        # Ensure backwards compatibility - merge agent_used and response_time into metadata
        if agent_used and "primary_agent" not in processed:
            processed["primary_agent"] = agent_used

        if response_time and "response_time" not in processed:
            processed["response_time"] = response_time

        # Add system metadata
        processed["stored_at"] = datetime.utcnow().isoformat()
        processed["metadata_version"] = "1.0"

        # Ensure all values are JSON serializable
        return self._make_json_serializable(processed)

    def _make_json_serializable(self, obj: Any) -> Any:
        """Ensure object is JSON serializable"""

        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, (int, float, str, bool)) or obj is None:
            return obj
        else:
            # Convert unknown types to string
            return str(obj)

    # ============================================================================
    # VECTOR SEARCH (If you're using embeddings)
    # ============================================================================

    async def search_messages_by_embedding(
        self,
        user_id: str,
        query_embedding: List[float],
        limit: int = 10,
        similarity_threshold: float = 0.8,
    ) -> List[Dict[str, Any]]:
        """Search messages using vector similarity (requires pgvector)"""
        if self.db is None:
            return []
        try:
            # This requires pgvector extension and embedding field in your Message model
            stmt = text("""
                        SELECT m.id,
                               m.content,
                               m.created_at,
                               c.id                                   as conversation_id,
                               c.title,
                               1 - (m.embedding <=> :query_embedding) as similarity
                        FROM messages m
                                 JOIN conversations c ON m.conversation_id = c.id
                        WHERE c.user_id = :user_id
                          AND m.embedding IS NOT NULL
                          AND 1 - (m.embedding <=> :query_embedding) > :threshold
                        ORDER BY m.embedding <=> :query_embedding
                LIMIT :limit
                        """)

            result = await self.db.execute(
                stmt,
                {
                    "query_embedding": query_embedding,
                    "user_id": user_id,
                    "threshold": similarity_threshold,
                    "limit": limit,
                },
            )

            results = []
            for row in result:
                results.append(
                    {
                        "message_id": str(row.id),
                        "content": row.content,
                        "created_at": row.created_at,
                        "conversation_id": str(row.conversation_id),
                        "conversation_title": row.title,
                        "similarity": row.similarity,
                    }
                )

            return results

        except Exception as e:
            logger.error(f"Failed to search by embedding: {e}")
            return []

    # ============================================================================
    # CHAT-SPECIFIC UTILITIES
    # ============================================================================

    async def conversation_exists(self, conversation_id: str, user_id: str) -> bool:
        """Check if a conversation exists and user has access"""
        logger.info(
            f"Checking existence of conversation {conversation_id} for user {user_id}"
        )
        if self.db is None:
            return False
        try:
            stmt = select(func.count(Conversation.id)).where(
                and_(
                    Conversation.id == conversation_id, Conversation.user_id == user_id
                )
            )
            result = await self.db.execute(stmt)
            count = result.scalar()
            return count > 0
        except Exception as e:
            logger.error(f"Failed to check conversation existence: {e}")
            return False

    async def get_conversation_context(self, conversation_id: str) -> Dict[str, Any]:
        """Get conversation context for chat processing"""
        if self.db is None:
            return {}
        try:
            # Get conversation details
            conv_stmt = select(Conversation).where(Conversation.id == conversation_id)
            conv_result = await self.db.execute(conv_stmt)
            conversation = conv_result.scalar_one_or_none()

            if not conversation:
                return {}

            # Get message count and recent activity
            msg_stmt = select(
                func.count(Message.id).label("message_count"),
                func.max(Message.created_at).label("last_activity"),
            ).where(Message.conversation_id == conversation_id)

            msg_result = await self.db.execute(msg_stmt)
            msg_stats = msg_result.first()

            return {
                "conversation_id": str(conversation.id),
                "title": conversation.title,
                "created_at": conversation.created_at,
                "message_count": msg_stats.message_count or 0,
                "last_activity": msg_stats.last_activity,
                "user_id": str(conversation.user_id),
            }

        except Exception as e:
            logger.error(f"Failed to get conversation context: {e}")
            return {}

    async def generate_message_embedding(self, content: str) -> Optional[List[float]]:
        """Generate embedding for message content (placeholder for your embedding service)"""

        try:
            # This would integrate with your embedding service (OpenAI, etc.)
            # For now, return None - you can implement this based on your needs

            # Example implementation:
            # from your_embedding_service import generate_embedding
            # embedding = await generate_embedding(content)
            # return embedding

            return None

        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            return None

    async def get_chat_suggestions(self, conversation_id: str) -> List[str]:
        """Get suggested responses/questions based on conversation history"""

        try:
            # Get recent messages
            recent_messages = await self.get_recent_messages(conversation_id, limit=3)

            if not recent_messages:
                return ["How can I help you today?", "What would you like to know?"]

            # Simple suggestion logic - you could enhance this with AI
            last_message = recent_messages[-1]

            suggestions = []

            if last_message.sender == "assistant":
                suggestions = [
                    "Can you explain that further?",
                    "That's helpful, thanks!",
                    "What about...?",
                ]
            else:
                suggestions = [
                    "Let me help you with that",
                    "I can provide more information",
                    "Would you like me to elaborate?",
                ]

            return suggestions[:3]

        except Exception as e:
            logger.error(f"Failed to get chat suggestions: {e}")
            return []

    # ============================================================================
    # PRIVATE UTILITY METHODS
    # ============================================================================

    async def _update_conversation_timestamp(self, conversation_id: str):
        """Update conversation's updated_at timestamp"""
        if self.db is None:
            return
        try:
            stmt = (
                update(Conversation)
                .where(Conversation.id == conversation_id)
                .values(updated_at=datetime.utcnow())
            )

            await self.db.execute(stmt)
            await self.db.commit()

        except Exception as e:
            logger.error(f"Failed to update conversation timestamp: {e}")

    # ============================================================================
    # HEALTH CHECK
    # ============================================================================

    async def health_check(self) -> Dict[str, Any]:
        """Check chat service health"""

        try:
            # Test database connection
            stmt = select(func.count(Message.id))
            result = await self.db.execute(stmt)
            message_count = result.scalar()

            return {
                "service": "chat_service",
                "status": "healthy",
                "database": "connected",
                "total_messages": message_count,
                "timestamp": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(f"Chat service health check failed: {e}")
            return {
                "service": "chat_service",
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat(),
            }

    async def get_completion(self, prompt: str) -> str:
        """Simple chat completion"""
        try:
            messages = [{"role": "user", "content": prompt}]
            # Call LLM with prompt, no tools
            llm_response = await get_chat_completion_raw(messages)
            return llm_response
        except Exception as e:
            logger.error(f"Failed to get chat completion: {e}")
            return "{}"
