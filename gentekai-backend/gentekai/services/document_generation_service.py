import os
import tempfile
from pathlib import Path
from typing import Any, Dict, Optional, Union
from docxtpl import Docx<PERSON><PERSON>plate
from docx import Document
import logging
from io import BytesIO


from gentekai.config import settings

logger = logging.getLogger(__name__)


class DocumentGenerationService:
    """Service for generating documents from templates with data filling capabilities using python-docx-template."""
    
    def __init__(self):
        self.template_dir = Path(__file__).parent.parent / "assets"
    
    def generate_cv_document(
        self, 
        cv_data: Dict[str, Any], 
        template_name: str = "cv_template.docx",
        output_filename: Optional[str] = None
    ) -> str:
        """
        Generate a CV document from template with provided data using python-docx-template.
        
        Args:
            cv_data: Dictionary containing CV data (structured_data from CVData model)
            template_name: Name of the template file in assets directory
            output_filename: Optional custom output filename
            
        Returns:
            Path to the generated document
        """
        try:
            # Load the template
            template_path = self.template_dir / template_name
            if not template_path.exists():
                raise FileNotFoundError(f"Template file not found: {template_path}")
            
            # Create
            doc = DocxTemplate(str(template_path))
            
            # Render the template with data
            doc.render(cv_data)
            
            # Generate output filename if not provided
            if not output_filename:
                candidate_name = cv_data.get("personal_info", {}).get("name", "cv")
                output_filename = f"{candidate_name.replace(' ', '_')}_generated.docx"
            
            # Create temporary file for output
            output_path = Path(tempfile.gettempdir()) / output_filename
            
            # Save the document
            doc.save(str(output_path))
            
            logger.info(f"CV document generated successfully: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error generating CV document: {str(e)}")
            raise
    