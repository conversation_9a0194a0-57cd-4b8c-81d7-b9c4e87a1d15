import asyncio
from datetime import datetime
import hashlib
import uuid
import httpx
import logging
from typing import List, Optional, Dict, Any, Tuple
from io import Bytes<PERSON>

from slack_sdk import WebClient

from gentekai.api.schemas.slack import SlackFile
from gentekai.services.document_context_service import DocumentContextService
from gentekai.services.slack.slack_file_downloader import SlackFileDownloader
from gentekai.storage.r2 import R2StorageService

logger = logging.getLogger(__name__)

class SlackFileHandler:
    """Service for handling Slack file uploads and downloads"""
    
    def __init__(self, slack_client: WebClient, bot_token: str = None):
        self.slack_client = slack_client
        self.bot_token = bot_token or slack_client.token
        self.r2_service = R2StorageService()
        self.downloader = SlackFileDownloader(token=self.bot_token, timeout=60)
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.downloader.__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.downloader.__aexit__(exc_type, exc_val, exc_tb)
       

    async def get_file_info(self, file_id: str) -> Optional[Dict[str, Any]]:
        """Get detailed file information from Slack API"""
        try:
            
            response = self.slack_client.files_info(file=file_id)
            return response.get("file")
        except Exception as e:
            logger.error(f"Error getting file info for {file_id}: {e}")
            return None

    
    async def upload_slack_files_to_r2(
        self, 
        files: List[Dict],
        batch_size: int = 10,
        download_retries: int = 3
    ) -> Tuple[List[str], List[Dict]]:
        """
        Download Slack files and upload them to R2 storage.
        
        Args:
            files: List of Slack file dictionaries
            batch_size: Number of files to process concurrently
            download_retries: Number of retry attempts for each download
            
        Returns:
            Tuple of (file_keys, upload_results)
        """
        if not files:
            return [], []

        file_keys = []
        upload_results = []
        
        # Process files in batches to avoid overwhelming resources
        for i in range(0, len(files), batch_size):
            batch = files[i:i + batch_size]
            batch_keys, batch_results = await self._process_batch(batch, download_retries)
            file_keys.extend(batch_keys)
            upload_results.extend(batch_results)
            
        return file_keys, upload_results
        
    async def _process_batch(
        self, 
        files: List[Dict], 
        download_retries: int
    ) -> Tuple[List[str], List[Dict]]:
        """Process a batch of files concurrently."""
        # Download all files in the batch concurrently
        download_tasks = []
        for file in files:
            task = self._download_and_prepare_file(file, download_retries)
            download_tasks.append(task)
            
        prepared_files = await asyncio.gather(*download_tasks, return_exceptions=True)
        
        # Filter out failed downloads
        files_to_upload = []
        file_keys = []
        
        for result in prepared_files:
            if isinstance(result, Exception):
                logger.error(f"Failed to process file: {result}")
            elif result:  # result is (file_key, upload_data)
                file_key, upload_data = result
                files_to_upload.append(upload_data)
                file_keys.append(file_key)
                
        # Upload all successfully downloaded files to R2
        upload_results = []
        if files_to_upload:
            try:
                upload_results = await self._upload_to_r2(files_to_upload)
                logger.info(f"Uploaded {len(upload_results)} files to R2")
            except Exception as e:
                logger.error(f"Error uploading files to R2: {e}")
                return [], []
                
        return file_keys, upload_results
        
    async def _download_and_prepare_file(
        self, 
        file: Dict,
        download_retries: int
    ) -> Optional[Tuple[str, Dict]]:
        """
        Download a single file and prepare it for upload.
        
        Returns:
            Tuple of (file_key, upload_data) or None if failed
        """
        try:
            # Extract file information
            file_name = file.get('name', 'unknown_file')
            file_size = file.get('size')
            file_url = file.get('url_private_download')
            
            if not file_url:
                logger.error(f"No download URL for file: {file_name}")
                return None
                
            # Download file from Slack
            logger.info(f"Downloading file: {file_name} ({file_size:,} bytes)" if file_size else f"Downloading file: {file_name}")
            
            file_content = await self.downloader.download_file(
                file_url=file_url,
                expected_size=file_size,
                file_name=file_name,
                max_retries=download_retries
            )
            
            if not file_content:
                logger.error(f"Failed to download file: {file_name}")
                return None
                
            # Generate unique file key
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            file_key = f"slack_files/{timestamp}_{unique_id}_{file_name}"
            
            # Prepare upload data
            upload_data = {
                "file_key": file_key,
                "content": file_content.getvalue(),
                "content_type": file.get('mimetype', 'application/octet-stream'),
                "metadata": {
                    "original_name": file_name,
                    "slack_file_id": file.get('id'),
                    "uploaded_at": datetime.now().isoformat(),
                    "original_size": file_size,
                }
            }
            
            logger.info(f"Prepared file for R2 upload: {file_name} -> {file_key}")
            return file_key, upload_data
            
        except Exception as e:
            logger.error(f"Error preparing file {file.get('name')} for upload: {e}")
            return None
            
    async def _upload_to_r2(self, files_to_upload: List[Dict]) -> List[Dict]:
        """
        Upload files to R2 storage.
        
        This method assumes your R2 service has async support.
        If it doesn't, you can use asyncio.to_thread for sync methods.
        """
        # If your R2 service is async
        if hasattr(self.r2_service, 'upload_multiple_files_async'):
            return await self.r2_service.upload_multiple_files(files_to_upload)
        else:
            # If your R2 service is sync, run it in a thread
            return await asyncio.to_thread(
                self.r2_service.upload_multiple_files,
                files_to_upload
            )
            
    async def upload_single_file_to_r2(
        self,
        file_url: str,
        file_name: str,
        file_size: Optional[int] = None,
        content_type: str = 'application/octet-stream',
        custom_key: Optional[str] = None
    ) -> Optional[Dict]:
        """
        Download a single file from Slack and upload to R2.
        
        Args:
            file_url: Slack file URL
            file_name: Original file name
            file_size: Expected file size
            content_type: MIME type of the file
            custom_key: Custom R2 key (if not provided, will generate one)
            
        Returns:
            Upload result dictionary or None if failed
        """
        # Download file
        file_content = await self.downloader.download_file(
            file_url=file_url,
            expected_size=file_size,
            file_name=file_name
        )
        
        if not file_content:
            return None
            
        # Generate file key if not provided
        if not custom_key:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            file_key = f"slack_files/{timestamp}_{unique_id}_{file_name}"
        else:
            file_key = custom_key
            
        # Upload to R2
        try:
            if hasattr(self.r2_service, 'upload_file_async'):
                result = await self.r2_service.upload_file(
                    file_key=file_key,
                    content=file_content.getvalue(),
                    content_type=content_type
                )
            else:
                result = await asyncio.to_thread(
                    self.r2_service.upload_file,
                    file_key=file_key,
                    content=file_content.getvalue(),
                    content_type=content_type
                )
            return result
        except Exception as e:
            logger.error(f"Failed to upload {file_name} to R2: {e}")
            return None
        
    async def process_file_upload_with_context(
        self, 
        files: List[Dict]
    ) -> Dict[str, Any]:
        """
        Process uploaded files, upload to R2, and prepare document context
        Returns: {
            "file_keys": [...],
            "upload_results": [...],

        }
        """
        if not files:
            return {
                "file_keys": [],
                "upload_results": [],
            }

        # Upload files to R2
        file_keys, upload_results = await self.upload_slack_files_to_r2(files)
        
        if not file_keys:
            return {
                "file_keys": [],
                "upload_results": [],
             
            }

        return {
            "file_keys": file_keys,
            "upload_results": upload_results,
        }
