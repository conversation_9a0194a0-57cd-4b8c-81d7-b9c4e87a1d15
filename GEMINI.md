# Gemini Workspace Configuration

This document outlines the project structure and specifies which files and directories should be ignored by the Gemini agent. This helps the agent focus on relevant source code and ignore unnecessary files like virtual environments, build artifacts, and dependencies.

## Project Structure

This repository contains a monorepo with two main services that work together in an agent-based architecture.

1.  **`gentekai-backend`**: A Python-based backend service (using FastAPI) that hosts multiple AI agents.
    -   **Agent Logic**: The core agent implementations are located in `gentekai-backend/gentekai/agent/`.
    -   **Functionality**: These agents are responsible for handling complex tasks. They achieve this by calling tools available in the Mission Control Plane (MCP).
    -   **Dependencies**: Managed by `gentekai-backend/requirements.txt`.
    -   **Database Migrations**: Handled by Alembic (`alembic.ini`).

2.  **`gentekai-sql-mcp-server`**: A TypeScript-based Node.js service that acts as the Mission Control Plane (MCP) for the agents.
    -   **MCP Logic**: The core MCP implementation is in `gentekai-sql-mcp-server/src/mcp/`.
    -   **Functionality**: It exposes a set of tools that the Python agents can call to perform specific actions. A key function of the MCP is to provide secure and structured access to the database.
    -   **Database Tools**: The MCP includes tools for interacting with the database. These tools are built on top of Prisma.
    -   **Database Schema**: The database schema is defined in `gentekai-sql-mcp-server/prisma/schema.prisma`.
    -   **Dependencies**: Managed by `package.json` and `pnpm-lock.yaml`.

## Architectural Flow

1.  A request is received by the `gentekai-backend`.
2.  An agent in `gentekai-backend/gentekai/agent/` is invoked to handle the request.
3.  The agent communicates with the `gentekai-sql-mcp-server` to execute necessary actions, such as database queries or other operations.
4.  The MCP uses its tools (e.g., Prisma clients) to interact with the database and returns the result to the agent.
5.  The agent processes the result and completes its task.

## Files and Directories to Exclude

To ensure the agent operates efficiently and avoids reading irrelevant or sensitive files, the following patterns should be excluded from file searches and reads.

```glob
# General
.DS_Store
.git/
.vscode/
.pytest_cache/
__pycache__/
*.pyc
*.egg-info/

# Python Virtual Environments
.venv/
gentekai-backend/.venv/

# Node.js Dependencies
gentekai-sql-mcp-server/node_modules/

# Build output
gentekai-sql-mcp-server/dist/

# Generated code
gentekai-sql-mcp-server/generated/

# Environment files & Secrets
*.env.*
.env.secrets
```
