# Project Setup Guide for GenTekai Backend and SQL MCP Server

## Introduction

This document provides detailed instructions for deploying the GenTekai Backend and SQL MCP Server projects locally using Docker Compose. The guide covers two primary options: running both services concurrently or deploying them independently. The objective is to ensure a stable and efficient development environment.

---

## Prerequisites

To proceed with the setup, your system must have the following:

- **Docker Desktop**: Ensure Docker is installed and running on your machine.
- **Docker Compose**: Docker Compose is typically integrated with Docker Desktop. Verify your Docker Compose version by executing the following command in your terminal:

---

## Option 1: Deploying GenTekai Backend and SQL MCP Server Concurrently

This option allows you to launch the GenTekai Backend, SQL MCP Server, and PostgreSQL database simultaneously using the `gentekai-backend/docker-compose.yml` file.

### Steps

1. **Configure Environment Variables**
   
   Copy the environment variable template and populate the necessary values. This file will contain sensitive information, so it's named `.env.secrets`.
   
   ```bash
   cp .env.secrets.template .env.secrets
   ```
   
   Then edit the `.env.secrets` file with your actual configuration values.

2. **Verify and Create Docker Network**
   
   Ensure that the `gentekai-network` Docker network exists. This is an external network used by Docker Compose to facilitate communication between services. If the network does not already exist, create it using the following command:
   
   ```bash
   docker network create gentekai-network
   ```

3. **Start Services**
   
   Navigate to the `gentekai-backend` directory and execute the following command to start all configured services:
   
   ```bash
   cd gentekai-backend
   docker compose up -d
   ```

### Service Descriptions

- **backend**: The GenTekai Backend service, built from `Dockerfile.dev`. This service is accessible via port `9876` (mapped to container port 8000).
- **db_mcp_server**: The SQL MCP Server service, built from the Dockerfile located in the `gentekai-sql-mcp-server` directory. This service is accessible via port `9877`.
- **db**: The PostgreSQL database, which includes the pgvector extension. This database is accessible via port `6543`.

> **Note:** Database data is persisted through the Docker volume `genai_db`, ensuring data integrity even if containers are restarted.

---

## Option 2: Deploying GenTekai Backend and SQL MCP Server Independently

If you prefer independent control over each service, you can launch the GenTekai Backend and SQL MCP Server separately using their respective production Docker Compose files.

> **Important:** Ensure that the PostgreSQL database is running and operational before starting either the Backend or the SQL MCP Server. You can use the Backend's `docker-compose.yml` file to start the database or any other method to run a PostgreSQL instance with the necessary configuration.

### Deploying GenTekai Backend

1. **Configure Environment Variables**
   
   Copy the environment variable template and populate the necessary values:
   
   ```bash
   cd gentekai-backend
   cp .env.secrets.template .env.secrets
   ```
   
   Then edit the `.env.secrets` file with your actual configuration values.

2. **Verify and Create Docker Network**
   
   Ensure that the `gentekai-network` Docker network exists:
   
   ```bash
   docker network create gentekai-network
   ```

3. **Start Backend Service**
   
   Navigate to the `gentekai-backend` directory and execute the following command to start the Backend service:
   
   ```bash
   cd gentekai-backend
   docker compose -f docker-compose.prod.yml up -d
   ```

### Deploying SQL MCP Server

1. **Configure Environment Variables**
   
   Copy the environment variable template and populate the necessary values:
   
   ```bash
   cd gentekai-sql-mcp-server
   cp .env.template .env
   ```
   
   Then edit the `.env` file with your actual configuration values.

2. **Verify and Create Docker Network**
   
   Ensure that the `gentekai-network` Docker network exists:
   
   ```bash
   docker network create gentekai-network
   ```

3. **Start SQL MCP Server**
   
   Navigate to the `gentekai-sql-mcp-server` directory and execute the following command to start the SQL MCP Server service:
   
   ```bash
   cd gentekai-sql-mcp-server
   docker compose -f docker-compose.prod.yml up -d
   ```

---

## Important Notes

- **Docker Network:** Both setup options require the `gentekai-network` Docker network to ensure proper communication between containers.
- **PostgreSQL Database:** The PostgreSQL database is a shared component and must be running before the Backend and SQL MCP Server can function correctly.
- **Environment Variables:** Environment variables must be accurately configured in the `.env.secrets` file (for the Backend) and the `.env` file (for the SQL MCP Server).
- **Dockerfile Usage:** The Backend service utilizes `Dockerfile.dev` for image building, while the SQL MCP Server uses its `Dockerfile` located in its own directory.
- **Data Persistence:** In the combined setup, database data is persisted via the `genai_db` Docker volume.

---

## Conclusion

This guide provides the essential steps for deploying the GenTekai Backend and SQL MCP Server locally using Docker Compose, offering flexible options for both combined and independent setups. Adhering to these instructions will help you establish an efficient and stable development environment.
