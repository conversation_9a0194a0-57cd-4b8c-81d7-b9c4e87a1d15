{"version": "0.2.0", "configurations": [{"name": "Python: FastAPI", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["gentekai.main:app", "--host", "0.0.0.0", "--port", "9876", "--reload"], "jinja": true, "justMyCode": true, "cwd": "${workspaceFolder}/gentekai-backend", "envFile": "${workspaceFolder}/gentekai-backend/.env.secrets"}, {"name": "Node.js: Debug gentekai-sql-mcp-server", "type": "node", "request": "launch", "program": "${workspaceFolder}/gentekai-sql-mcp-server/src/index.ts", "cwd": "${workspaceFolder}/gentekai-sql-mcp-server", "runtimeArgs": ["--loader", "ts-node/esm"], "env": {"NODE_ENV": "development"}, "skipFiles": ["<node_internals>/**"], "outFiles": ["${workspaceFolder}/gentekai-sql-mcp-server/dist/**/*.js"]}]}