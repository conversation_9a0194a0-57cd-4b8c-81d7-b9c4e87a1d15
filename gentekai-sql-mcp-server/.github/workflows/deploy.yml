name: Deploy to Production

on:
  push:
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      deploy:
        description: 'Deploy to production or development'
        required: true
        default: 'true'
        type: boolean

env:
  PROJECT_NAME: gentekai-sql-mcp-server

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-24.04
    container: node:20
    timeout-minutes: 10
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install dependencies
        run: npm install
      # - name: Run tests
      #   run: npm test
      #   env:
      #     CI: true

  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set environment variables based on branch
        run: |
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "DOCKER_COMPOSE_FILE=docker-compose.prod.yml" >> $GITHUB_ENV
            echo "HEALTH_CHECK_URL=http://localhost:9879/-/health" >> $GITHUB_ENV
            echo "PROJECT_PREFIX=${{ env.PROJECT_NAME }}-prod" >> $GITHUB_ENV
          else
            echo "DOCKER_COMPOSE_FILE=docker-compose.dev.yml" >> $GITHUB_ENV
            echo "HEALTH_CHECK_URL=http://localhost:9877/-/health" >> $GITHUB_ENV
            echo "PROJECT_PREFIX=${{ env.PROJECT_NAME }}-dev" >> $GITHUB_ENV
          fi

      - name: Build Docker image
        run: |
          docker build -t ${{ env.PROJECT_NAME }}:${{ github.sha }} .
          docker save ${{ env.PROJECT_NAME }}:${{ github.sha }} > /tmp/image.tar

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Add server to known hosts
        run: |
          ssh-keyscan -H ${{ secrets.SSH_HOST }} >> ~/.ssh/known_hosts
          chmod 600 ~/.ssh/known_hosts

      - name: Deploy to server
        run: |
          # Transfer files
          scp /tmp/image.tar ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/${{ env.PROJECT_NAME }}/
          scp ${{ env.DOCKER_COMPOSE_FILE }} ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:~/${{ env.PROJECT_NAME }}/

          # Deploy on server
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "
            set -e
            
            cd ~/${{ env.PROJECT_NAME }}

            echo 'Saving current deployment info...'
            docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} ps || true

            if docker image inspect ${{ env.PROJECT_NAME }}:latest >/dev/null 2>&1; then
              echo 'Backing up current image...'
              docker tag ${{ env.PROJECT_NAME }}:latest ${{ env.PROJECT_NAME }}:previous
            else
              echo 'No existing image to backup'
            fi

            # Load new image
            echo 'Loading new image...'
            docker load -i image.tar
            docker tag ${{ env.PROJECT_NAME }}:${{ github.sha }} ${{ env.PROJECT_NAME }}:latest

            # Deploy
            echo 'Stopping current containers...'
            docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} down --timeout 30

            echo 'Starting new deployment...'
            docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} up -d

            echo 'Waiting for services to start...'
            sleep 30

            # Health check
            echo 'Performing health check...'
            HEALTH_CHECK_PASSED=false
            for i in {1..12}; do
              if curl -f --max-time 5 ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
                echo '✅ Health check passed!'
                HEALTH_CHECK_PASSED=true
                break
              fi
              echo \"Health check attempt \$i/12 failed, waiting...\"
              sleep 15
            done

            # Rollback if health check failed
            if [ \"\$HEALTH_CHECK_PASSED\" = false ]; then
              echo '❌ Health check failed! Initiating rollback...'
              
              # Stop failed deployment
              docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} down --timeout 30
              
              # Check if previous image exists for rollback
              if docker image inspect ${{ env.PROJECT_NAME }}:previous >/dev/null 2>&1; then
                echo 'Rolling back to previous image...'
                docker tag ${{ env.PROJECT_NAME }}:previous ${{ env.PROJECT_NAME }}:latest
                docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} up -d
                
                # Verify rollback
                sleep 30
                for i in {1..6}; do
                  if curl -f --max-time 5 ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
                    echo '✅ Rollback successful!'
                    break
                  fi
                  echo \"Rollback verification attempt \$i/6...\"
                  sleep 10
                done
              else
                echo '⚠️ No previous image available for rollback!'
              fi
              
              echo '❌ Deployment failed and rollback completed'
              exit 1
            fi

            # Final verification
            echo 'Final deployment verification...'
            docker compose -p ${{ env.PROJECT_PREFIX }} -f ${{ env.DOCKER_COMPOSE_FILE }} ps
            
            # Test one more time to be sure
            if curl -f --max-time 10 ${{ env.HEALTH_CHECK_URL }} >/dev/null 2>&1; then
              echo '✅ Deployment successful and verified!'
              
              # Cleanup old images (keep only latest and previous)
              echo 'Cleaning up old images...'
              docker images ${{ env.PROJECT_NAME }} --format '{{.Repository}}:{{.Tag}}' | grep -v ':latest\|:previous' | xargs -r docker rmi || true
              
              echo 'Current images:'
              docker images ${{ env.PROJECT_NAME }} --format 'table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}\t{{.Size}}'
            else
              echo '❌ Final verification failed!'
              exit 1
            fi

            # Cleanup
            rm -f image.tar
          "
