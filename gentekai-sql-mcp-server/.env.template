# Environment Configuration
NODE_ENV="development" # Options: 'development', 'production'
PORT="3002"            # The port your server will listen on
HOST="localhost"       # Hostname for the server

# CORS Settings
CORS_ORIGIN="http://localhost:3002" # Allowed CORS origin, adjust as necessary

# Rate Limiting
COMMON_RATE_LIMIT_WINDOW_MS="1000"  # Window size for rate limiting (ms)
COMMON_RATE_LIMIT_MAX_REQUESTS="20" # Max number of requests per window per IP
DATABASE_URL="postgres://postgres:postgres@localhost:6543/dev_db" # Database connection string

#----Dev env (optional)----#
REMOTE_HOST=0.0.0.0
REMOTE_USER=ubuntu
LOCAL_PORT=6543
REMOTE_PORT=6543
DATABASE_NAME=dev_db
