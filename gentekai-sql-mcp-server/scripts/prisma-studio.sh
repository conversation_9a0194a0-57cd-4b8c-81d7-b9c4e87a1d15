#!/bin/bash

# Function to load .env file
load_env() {
    if [ ! -f .env ]; then
        echo "❌ .env file not found!"

EOF
        exit 1
    fi
    
    echo "📁 Loading configuration from .env..."
    
    # Load .env file, ignoring comments and empty lines
    while IFS= read -r line; do
        # Skip comments and empty lines
        if [[ $line =~ ^[[:space:]]*# ]] || [[ -z "${line// }" ]]; then
            continue
        fi
        
        # Export the variable
        if [[ $line == *"="* ]]; then
            export "$line"
        fi
    done < .env
}

# Load environment variables
load_env

# Validate required variables
required_vars=("REMOTE_HOST" "REMOTE_USER" "LOCAL_PORT" "REMOTE_PORT")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Missing required environment variable: $var"
        exit 1
    fi
done

# Set defaults
DATABASE_NAME=${DATABASE_NAME:-dev_db}
DATABASE_URL="postgresql://postgres:postgres@localhost:${LOCAL_PORT}/${DATABASE_NAME}"

echo "🚀 Starting SSH tunnel to ${REMOTE_USER}@${REMOTE_HOST}..."
echo "🔗 Forwarding localhost:${LOCAL_PORT} -> ${REMOTE_HOST}:${REMOTE_PORT}"

# Start SSH tunnel in background
ssh -f -N -L ${LOCAL_PORT}:localhost:${REMOTE_PORT} ${REMOTE_USER}@${REMOTE_HOST}

# Check if tunnel was established
if [ $? -eq 0 ]; then
    echo "✅ SSH tunnel established on port ${LOCAL_PORT}"
    
    # Wait a moment for tunnel to be ready
    sleep 2
    
    echo "🎨 Starting Prisma Studio..."
    echo "📱 Prisma Studio will open at: http://localhost:5555"
    echo "🗄️  Database: ${DATABASE_URL}"
    
    DATABASE_URL="${DATABASE_URL}" npx prisma studio
    
    # Clean up: Kill the SSH tunnel when Prisma Studio exits
    echo "🧹 Cleaning up SSH tunnel..."
    pkill -f "ssh.*-L ${LOCAL_PORT}:localhost:${REMOTE_PORT}"
    echo "✅ Done!"
else
    echo "❌ Failed to establish SSH tunnel"
    exit 1
fi