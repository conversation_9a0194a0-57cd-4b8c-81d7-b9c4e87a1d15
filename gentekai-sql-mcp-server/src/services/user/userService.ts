import { prisma } from "@/libs/prisma";
import { type Prisma, user_role, type users } from "../../../generated/prisma";
import { getCurrentUserId } from "@/common/utils/ContextAwareSSETransport";

export interface CreateUserInput {
	clerk_user_id?: string;
	slack_user_id?: string; // Optional Slack user ID for Slack auth integration
	email: string;
	first_name?: string;
	last_name?: string;
	role?: user_role;
	profile?: object;
}

export interface UpdateUserInput {
	clerk_user_id?: string;
	slack_user_id?: string; // Optional Slack user ID for Slack auth integration
	email?: string;
	first_name?: string;
	last_name?: string;
	role?: user_role;
	profile?: object;
}

export interface ListUsersFilters {
	role?: user_role;
	search?: string; // Search in first_name, last_name, email
	limit?: number;
	offset?: number;
}

export class UserService {
	private async validateAdminAccess() {
		const currentUser = await this.getCurrentUser();
		if (currentUser.role !== user_role.ADMIN) {
			throw new Error("Only admin users can perform this action");
		}
	}

	async createUser(userData: CreateUserInput): Promise<users> {
		await this.validateAdminAccess();

		const { clerk_user_id, email, first_name, last_name, role = user_role.USER, profile, slack_user_id } = userData;

		// Check if user already exists
		const whereConditions: Prisma.usersWhereInput = {
			OR: [{ email }, ...(clerk_user_id ? [{ clerk_user_id }] : [])],
		};

		const existingUser = await prisma.users.findFirst({
			where: whereConditions,
		});

		if (existingUser) {
			throw new Error("User with this clerk_user_id or email already exists");
		}

		const user = await prisma.users.create({
			data: {
				clerk_user_id,
				slack_user_id,
				email,
				first_name,
				last_name,
				role,
				profile: profile || {},
				is_active: true,
				created_at: new Date(),
				updated_at: new Date(),
			},
		});

		return user;
	}

	async getCurrentUser(): Promise<users> {
		const currentUser = await prisma.users.findFirst({
			where: { id: getCurrentUserId() },
		});
		if (!currentUser) {
			throw new Error("Current user not found");
		}
		return currentUser;
	};

	async getUserById(id: string): Promise<users | null> {
		await this.validateAdminAccess();

		return await prisma.users.findFirst({
			where: { id, is_active: true },
		});
	}
	async getUserByIds(id: string): Promise<users | null> {
		await this.validateAdminAccess();

		return await prisma.users.findFirst({
			where: { id, is_active: true },
		});
	}

	async getUserBySlackId(slack_user_id: string): Promise<users | null> {
		await this.validateAdminAccess();

		return await prisma.users.findFirst({
			where: { slack_user_id, is_active: true },
		});
	}

	async getUserByClerkId(clerk_user_id: string): Promise<users | null> {
		await this.validateAdminAccess();

		return await prisma.users.findFirst({
			where: { clerk_user_id, is_active: true },
		});
	}

	async getUserByEmail(email: string): Promise<users | null> {
		await this.validateAdminAccess();

		return await prisma.users.findFirst({
			where: { email, is_active: true },
		});
	}

	async updateUser(id: string, updates: UpdateUserInput): Promise<users> {
		await this.validateAdminAccess();

		const user = await prisma.users.findFirst({
			where: { id, is_active: true },
		});

		if (!user) {
			throw new Error("User not found");
		}

		// Check for email conflicts if email is being updated
		if (updates.email && updates.email !== user.email) {
			const emailExists = await prisma.users.findFirst({
				where: {
					email: updates.email,
					id: { not: id },
				},
			});
			if (emailExists) {
				throw new Error("Email already in use by another user");
			}
		}

		// Check for clerk_user_id conflicts if it's being updated
		if (updates.clerk_user_id && updates.clerk_user_id !== user.clerk_user_id) {
			const clerkIdExists = await prisma.users.findFirst({
				where: {
					clerk_user_id: updates.clerk_user_id,
					id: { not: id },
				},
			});
			if (clerkIdExists) {
				throw new Error("Clerk User ID already in use by another user");
			}
		}

		// Handle profile updates (merge with existing profile)
		let updatedProfile = updates.profile;
		if (updates.profile && user.profile) {
			updatedProfile = {
				...(user.profile as object),
				...updates.profile,
			};
		}

		const updatedUser = await prisma.users.update({
			where: { id },
			data: {
				...updates,
				profile: updatedProfile,
				updated_at: new Date(),
			},
		});

		return updatedUser;
	}

	async listUsers(filters: ListUsersFilters = {}): Promise<{ users: users[]; total: number }> {
		await this.validateAdminAccess();

		const { role, search, limit = 50, offset = 0 } = filters;

		// Build where conditions
		const whereConditions: Prisma.usersWhereInput = {
			is_active: true,
		};

		if (role) {
			whereConditions.role = role;
		}

		if (search) {
			whereConditions.OR = [
				{ first_name: { contains: search, mode: "insensitive" } },
				{ last_name: { contains: search, mode: "insensitive" } },
				{ email: { contains: search, mode: "insensitive" } },
			];
		}

		// Execute both queries in parallel
		const [users, total] = await Promise.all([
			prisma.users.findMany({
				where: whereConditions,
				orderBy: { created_at: "desc" },
				take: limit,
				skip: offset,
			}),
			prisma.users.count({
				where: whereConditions,
			}),
		]);

		return { users, total };
	}

	async deleteUser(id: string): Promise<void> {
		await this.validateAdminAccess();

		const user = await prisma.users.findFirst({
			where: { id, is_active: true },
		});

		if (!user) {
			throw new Error("User not found");
		}

		await prisma.users.update({
			where: { id },
			data: {
				is_active: false, // Soft delete by setting is_active to false
				updated_at: new Date(),
			},
		});
	}

	async updateUserProfile(id: string, profileUpdates: object): Promise<users> {
		await this.validateAdminAccess();

		const user = await prisma.users.findFirst({
			where: { id, is_active: true },
		});

		if (!user) {
			throw new Error("User not found");
		}

		const updatedProfile = {
			...((user.profile as object) || {}),
			...profileUpdates,
		};

		const updatedUser = await prisma.users.update({
			where: { id },
			data: {
				profile: updatedProfile,
				updated_at: new Date(),
			},
		});

		return updatedUser;
	}
}
