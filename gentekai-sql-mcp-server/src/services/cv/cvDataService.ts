import { prisma } from "@/libs/prisma";
import { user_role, type Prisma, type cv_data } from "../../../generated/prisma";
import { type HRTaskResult, HRTaskStatus } from "@/common/models/mcpToolResponse";
import { UserService } from "../user/userService";

export interface CreateCvDataInput {
	user_id: string;
	name: string;
	raw_data?: string;
	data: object;
	is_public?: boolean;
	source?: string;
}

export interface UpdateCvDataInput {
	name?: string;
	data?: object;
	is_public?: boolean;
	source?: string;
}

export interface ListCvDataFilters {
	user_id?: string;
	is_public?: boolean;
	source?: string;
	search?: string; // Search in name field
	limit?: number;
	offset?: number;
}

export class CvDataService {
	private userService = new UserService();

	async createCvData(cvData: CreateCvDataInput): Promise<HRTaskResult> {
		const { user_id, name, raw_data, data, is_public = false, source } = cvData;

		// Verify user exists
		const user = await prisma.users.findUnique({
			where: { id: user_id },
		});

		if (!user) {
			console.warn(`MCP Tool: create_cv_data failed: User not found`);
			return {
				user_response: "User not found",
				task_status: HRTaskStatus.FAILED,
				metadata: { reason: "user_not_found" },
			};
		}

		// Check if CV name already exists for this user
		const existingCv = await prisma.cv_data.findFirst({
			where: {
				user_id,
				name,
			},
		});

		if (existingCv) {
			console.warn(`MCP Tool: create_cv_data failed: CV with this name already exists for this user`);
			return {
				user_response: "CV with this name already exists",
				task_status: HRTaskStatus.FAILED,
				metadata: { reason: "duplicate_cv_name", cv_id: existingCv.id },
			};
		}

		const cv = await prisma.cv_data.create({
			data: {
				user_id,
				name,
				raw_data,
				data,
				is_public,
				source,
				created_at: new Date(),
				updated_at: new Date(),
			},
		});

		console.log("MCP Tool: create_cv_data result:", `Created CV data with ID: ${cv.id}`);
		return {
			user_response: "CV created successfully",
			task_status: HRTaskStatus.COMPLETED,
			entities_created: [cv.id.toString()],
			metadata: { cv_id: cv.id, source },
		};

	}

	async getCvDataById(id: number): Promise<cv_data | null> {
		return await prisma.cv_data.findUnique({
			where: { id },
			include: {
				users: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
			},
		});
	}

	async getCvDataByUser(user_id: string): Promise<cv_data[]> {
		// Verify user exists
		const user = await prisma.users.findUnique({
			where: { id: user_id },
		});

		if (!user) {
			throw new Error("User not found");
		}

		return await prisma.cv_data.findMany({
			where: { user_id },
			orderBy: { updated_at: "desc" },
		});
	}

	async updateCvData(id: number, updates: UpdateCvDataInput): Promise<cv_data> {
		const existingCv = await prisma.cv_data.findUnique({
			where: { id },
		});

		if (!existingCv) {
			throw new Error("CV not found");
		}

		// Check for name conflicts if name is being updated
		if (updates.name && updates.name !== existingCv.name) {
			const nameExists = await prisma.cv_data.findFirst({
				where: {
					user_id: existingCv.user_id,
					name: updates.name,
					id: { not: id },
				},
			});

			if (nameExists) {
				throw new Error("CV with this name already exists for this user");
			}
		}

		// Handle data updates (merge with existing data if partial update)
		let updatedData = updates.data;
		if (updates.data && existingCv.data) {
			updatedData = {
				...(existingCv.data as object),
				...updates.data,
			};
		}

		const updatedCv = await prisma.cv_data.update({
			where: { id },
			data: {
				...updates,
				data: updatedData,
				updated_at: new Date(),
			},
		});

		return updatedCv;
	}

	async deleteCvData(id: number): Promise<void> {
		const cv = await prisma.cv_data.findUnique({
			where: { id },
		});

		if (!cv) {
			throw new Error("CV not found");
		}

		await prisma.cv_data.delete({
			where: { id },
		});
	}

	async listCvData(filters: ListCvDataFilters = {}): Promise<{ cvs: cv_data[]; total: number }> {
		const { user_id, is_public, source, search, limit = 50, offset = 0 } = filters;

		let query = "SELECT id, user_id, name, data, created_at, updated_at, is_public, source FROM cv_data";
		let countQuery = "SELECT COUNT(*) FROM cv_data";
		const conditions: string[] = [];
		const params: (string | number | boolean)[] = [];
		let paramIndex = 1;

		const currentUser = await this.userService.getCurrentUser();

		if (currentUser?.role !== user_role.ADMIN) {
			if (user_id) 
				throw new Error("Only admins can query by user ID");

			conditions.push(`user_id = $${paramIndex++}::uuid`);
			params.push(currentUser.id);
		} else if (user_id) {
			conditions.push(`user_id = $${paramIndex++}::uuid`);
			params.push(user_id);
		}

		if (is_public !== undefined) {
			conditions.push(`is_public = $${paramIndex++}`);
			params.push(is_public);
		}

		if (source) {
			conditions.push(`source = $${paramIndex++}`);
			params.push(source);
		}

		if (search) {
			conditions.push(`(name ILIKE $${paramIndex} OR data::text ILIKE $${paramIndex + 1})`);
			params.push(`%${search}%`, `%${search}%`);
			paramIndex += 2;
		}

		if (conditions.length > 0) {
			const whereClause = ` WHERE ${conditions.join(" AND ")}`;
			query += whereClause;
			countQuery += whereClause;
		}

		query += ` ORDER BY updated_at DESC LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
		params.push(limit, offset);

		const totalResult = await prisma.$queryRawUnsafe<{ count: bigint }[]>(countQuery, ...params.slice(0, paramIndex - 2));
		const total = Number(totalResult[0].count);

		const cvs = await prisma.$queryRawUnsafe<cv_data[]>(query, ...params);

		// Manually fetch user data to avoid N+1 problem and for security
		const userIds = [...new Set(cvs.map((cv) => cv.user_id))];
		if (userIds.length > 0) {
			const users = await prisma.users.findMany({
				where: {
					id: { in: userIds },
				},
				select: {
					id: true,
					email: true,
					first_name: true,
					last_name: true,
				},
			});

			const userMap = new Map(users.map((u) => [u.id, u]));
			cvs.forEach((cv: any) => {
				cv.users = userMap.get(cv.user_id);
			});
		}

		return { cvs, total };
	}

	async updateCvDataData(id: number, dataUpdates: object): Promise<cv_data> {
		const cv = await prisma.cv_data.findUnique({
			where: { id },
		});

		if (!cv) {
			throw new Error("CV not found");
		}

		const updatedData = {
			...((cv.data as object) || {}),
			...dataUpdates,
		};

		const updatedCv = await prisma.cv_data.update({
			where: { id },
			data: {
				data: updatedData,
				updated_at: new Date(),
			},
		});

		return updatedCv;
	}

	// async duplicateCvData(id: number, newName?: string): Promise<cv_data> {
	// 	const originalCv = await prisma.cv_data.findUnique({
	// 		where: { id },
	// 	});

	// 	if (!originalCv) {
	// 		throw new Error("CV not found");
	// 	}

	// 	const duplicateName = newName || `${originalCv.name} (Copy)`;

	// 	// Check if duplicate name already exists
	// 	const nameExists = await prisma.cv_data.findFirst({
	// 		where: {
	// 			user_id: originalCv.user_id,
	// 			name: duplicateName,
	// 		},
	// 	});

	// 	if (nameExists) {
	// 		throw new Error("CV with this name already exists for this user");
	// 	}

	// 	const duplicatedCv = await prisma.cv_data.create({
	// 		data: {
	// 			user_id: originalCv.user_id,
	// 			name: duplicateName,
	// 			data: originalCv.data as object,
	// 			is_public: false, // Set to private by default for duplicates
	// 			source: originalCv.source,
	// 			created_at: new Date(),
	// 			updated_at: new Date(),
	// 		},
	// 	});

	// 	return duplicatedCv;
	// }

	async toggleCvVisibility(id: number): Promise<cv_data> {
		const cv = await prisma.cv_data.findUnique({
			where: { id },
		});

		if (!cv) {
			throw new Error("CV not found");
		}

		const updatedCv = await prisma.cv_data.update({
			where: { id },
			data: {
				is_public: !cv.is_public,
				updated_at: new Date(),
			},
		});

		return updatedCv;
	}

	async getPublicCvData(): Promise<cv_data[]> {
		return await prisma.cv_data.findMany({
			where: { is_public: true },
			include: {
				users: {
					select: {
						id: true,
						first_name: true,
						last_name: true,
						// Don't include email for public CVs for privacy
					},
				},
			},
			orderBy: { updated_at: "desc" },
		});
	}

	async getCvDataBySource(source: string): Promise<cv_data[]> {
		return await prisma.cv_data.findMany({
			where: { source },
			include: {
				users: {
					select: {
						id: true,
						email: true,
						first_name: true,
						last_name: true,
					},
				},
			},
			orderBy: { updated_at: "desc" },
		});
	}
}
