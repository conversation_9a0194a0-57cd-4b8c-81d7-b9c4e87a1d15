import { UserService } from "@/services/user/userService";
import type { McpServer } from "@modelcontextprotocol/sdk/server/mcp";
import { z } from "zod";
import { user_role } from "../../../generated/prisma";
// Initialize user service
const userService = new UserService();

// Schema definitions for validation
const CreateUserSchema = z.object({
	clerk_user_id: z.string().optional(),
	slack_user_id: z.string().optional(),
	email: z.string().email(),
	first_name: z.string().optional(),
	last_name: z.string().optional(),
	role: z.nativeEnum(user_role).optional().default(user_role.USER),
	profile: z.record(z.any()).optional(),
});

const UpdateUserSchema = z.object({
	user_id: z.string(),
	clerk_user_id: z.string().optional(),
	slack_user_id: z.string().optional(),
	email: z.string().email().optional(),
	first_name: z.string().optional(),
	last_name: z.string().optional(),
	role: z.nativeEnum(user_role).optional(),
	profile: z.record(z.any()).optional(),
});

const GetUserSchema = z.object({
	user_id: z.string(),
});

const GetUserBySlackIdSchema = z.object({
	slack_user_id: z.string(),
});

const ListUsersSchema = z.object({
	role: z.nativeEnum(user_role).optional(),
	search: z.string().optional(),
	limit: z.number().min(1).max(100).optional().default(50),
	offset: z.number().min(0).optional().default(0),
});

const UpdateProfileSchema = z.object({
	user_id: z.string(),
	profile: z.record(z.any()),
});
export const addUserManagementTools = (server: McpServer): McpServer => {
	// Create User Tool
	server.tool(
		"create_user",
		"Create a new user account with email, name, role, and profile information",
		{
			clerk_user_id: z.string().optional().describe("Optional Clerk user ID for authentication integration"),
			slack_user_id: z.string().optional().describe("Optional Slack user ID for slack auth integration"),
			email: z.string().email().describe("User's email address (required and must be unique)"),
			first_name: z.string().optional().describe("User's first name"),
			last_name: z.string().optional().describe("User's last name"),
			role: z
				.nativeEnum(user_role)
				.optional()
				.default(user_role.USER)
				.describe("User role (ADMIN, USER, OWNER, EXTERNAL)"),
			profile: z.record(z.any()).optional().describe("Additional profile data as key-value pairs"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: create_user called with args:", args);

				const validatedArgs = CreateUserSchema.parse(args);
				const user = await userService.createUser(validatedArgs);

				const result = {
					success: true,
					message: "User created successfully",
					user,
				};

				console.log("MCP Tool: create_user result:", `Created user with ID: ${user.id}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: create_user error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get User Tool
	server.tool(
		"get_user",
		"Retrieve detailed user information including first name, last name, email, role, and profile data",
		{
			user_id: z.string().describe("User id to fetch data"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: get_user called with args:", args);

				const validatedArgs = GetUserSchema.parse(args);
				const user = await userService.getUserById(validatedArgs.user_id);

				if (!user) {
					const result = {
						success: false,
						message: "User not found",
					};

					console.log("MCP Tool: get_user result:", result);

					return {
						content: [
							{
								type: "text",
								text: JSON.stringify(result, null, 2),
							},
						],
					};
				}

				const result = {
					success: true,
					user,
				};

				console.log("MCP Tool: get_user result:", `Found user: ${user.email}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_user error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get User Tool
	server.tool(
		"get_user_by_slack_id",
		"Retrieve user information by Slack user ID",
		{
			slack_user_id: z.string().describe("Slack user ID to fetch data"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: get_user_by_slack_id called with args:", args);

				const validatedArgs = GetUserBySlackIdSchema.parse(args);
				const user = await userService.getUserBySlackId(validatedArgs.slack_user_id);

				if (!user) {
					const result = {
						success: false,
						message: "User not found",
					};

					console.log("MCP Tool: get_user result:", result);

					return {
						content: [
							{
								type: "text",
								text: JSON.stringify(result, null, 2),
							},
						],
					};
				}

				const result = {
					success: true,
					user,
				};

				console.log("MCP Tool: get_user result:", `Found user: ${user.email}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_user error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Update User Tool
	server.tool(
		"update_user",
		"Update existing user information such as name, email, or role",
		{
			user_id: z.string().describe("User id to update"),
			clerk_user_id: z.string().optional().describe("Updated Clerk user ID"),
			slack_user_id: z.string().optional().describe("Updated Slack user ID"),
			email: z.string().email().optional().describe("Updated email address"),
			first_name: z.string().optional().describe("Updated first name"),
			last_name: z.string().optional().describe("Updated last name"),
			role: z.nativeEnum(user_role).optional().describe("Updated role (ADMIN, USER, OWNER, EXTERNAL)"),
			profile: z.record(z.any()).optional().describe("Updated profile data (will be merged)"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: update_user called with args:", args);

				const validatedArgs = UpdateUserSchema.parse(args);
				const { user_id, ...updates } = validatedArgs;
				const user = await userService.updateUser(user_id, updates);

				const result = {
					success: true,
					message: "User updated successfully",
					user,
				};

				console.log("MCP Tool: update_user result:", `Updated user: ${user.email}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: update_user error:", error);
				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// List Users Tool
	server.tool(
		"list_users",
		"List all users with optional filtering by role or search criteria",
		{
			role: z.nativeEnum(user_role).optional().describe("Filter by role (ADMIN, USER, OWNER, EXTERNAL)"),
			search: z.string().optional().describe("Search in first_name, last_name, and email"),
			limit: z.number().min(1).max(100).optional().default(50).describe("Limit number of results (1-100)"),
			offset: z.number().min(0).optional().default(0).describe("Offset for pagination"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: list_users called with args:", args);

				const validatedArgs = ListUsersSchema.parse(args);
				const { users, total } = await userService.listUsers(validatedArgs);

				const result = {
					success: true,
					total,
					count: users.length,
					users,
					pagination: {
						limit: validatedArgs.limit,
						offset: validatedArgs.offset,
						hasMore: validatedArgs.offset + users.length < total,
					},
				};

				console.log("MCP Tool: list_users result:", `Found ${users.length} users out of ${total} total`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: list_users error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Delete User Tool
	server.tool(
		"delete_user",
		"Delete a user account permanently",
		{
			user_id: z.string().describe("User id to delete"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: delete_user called with args:", args);

				const { user_id } = z
					.object({
						user_id: z.string(),
					})
					.parse(args);

				await userService.deleteUser(user_id);

				const result = {
					success: true,
					message: "User deleted successfully",
				};

				console.log("MCP Tool: delete_user result:", `Deleted user with ID: ${user_id}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: delete_user error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Update User Profile Tool
	server.tool(
		"update_user_profile",
		"Update user profile data and preferences",
		{
			user_id: z.string().describe("User id to update"),
			profile: z.record(z.any()).describe("Profile data to merge with existing profile"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: update_user_profile called with args:", args);

				const validatedArgs = UpdateProfileSchema.parse(args);
				const user = await userService.updateUserProfile(validatedArgs.user_id, validatedArgs.profile);

				const result = {
					success: true,
					message: "User profile updated successfully",
					user,
				};

				console.log("MCP Tool: update_user_profile result:", `Updated profile for user: ${user.email}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: update_user_profile error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get Current User Tool
	server.tool(
		"get_current_user",
		"Retrieve information about the current user who owns request",
		{},
		async () => {
			try {
				console.log("MCP Tool: get_current_user called");
				const user = await userService.getCurrentUser();
				console.log("MCP Tool: get_current_user result:", `Current user: ${user.email}`);
				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(user, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_current_user error:", error);
				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);
	return server;
};
