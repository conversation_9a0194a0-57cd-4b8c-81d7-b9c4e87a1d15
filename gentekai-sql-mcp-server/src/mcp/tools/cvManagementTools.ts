import { CvDataService } from "@/services/cv/cvDataService";
import type { McpServer } from "@modelcontextprotocol/sdk/server/mcp";
import { z } from "zod";
import { getCurrentUserId } from "@/common/utils/ContextAwareSSETransport";

// Initialize CV data service
const cvDataService = new CvDataService();

// CV Template Structure Schema
const CvTemplateSchema = z.object({
	personal_info: z.object({
		name: z.string().describe("Full name of the person"),
		job_title: z.string().describe("Current or desired job title"),
	}),
	about_me: z.string().describe("Personal summary or about me section"),
	key_skills: z.array(z.object({
		type: z.string().describe("Category or type of skills (e.g., 'Technical', 'Soft Skills', 'Languages')"),
		list: z.array(z.string()).describe("List of skills in this category"),
	})).describe("Array of skill categories with their respective skill lists"),
	education: z.object({
		degree: z.string().describe("Degree or qualification obtained"),
		school: z.string().describe("Educational institution name"),
	}),
	work_experiences: z.array(z.object({
		start_date: z.string().describe("Start date of employment (format: YYYY-MM or YYYY-MM-DD)"),
		end_date: z.string().describe("End date of employment (format: YYYY-MM or YYYY-MM-DD, or 'Present' for current position)"),
		position: z.string().describe("Job title or position held"),
		company: z.string().describe("Company or organization name"),
		location: z.string().describe("Location of the job (city, country, or remote)"),
		description: z.string().describe("Description of responsibilities and achievements"),
	})).describe("Array of work experience entries"),
	projects: z.array(z.object({
		name: z.string().describe("Project name"),
		duration: z.string().describe("Project duration or timeline"),
		description: z.string().describe("Project description and objectives"),
		main_stacks: z.array(z.string()).describe("Main technologies or frameworks used"),
		responsibilities: z.array(z.string()).describe("List of responsibilities or contributions to the project"),
	})).describe("Array of project experiences"),
});

// Schema definitions for validation
const CreateCvDataSchema = z.object({
	name: z.string().min(1),
	raw_data: z.string(),
	data: CvTemplateSchema.describe("CV data must match the default template structure"),
	is_public: z.boolean().optional().default(false),
	source: z.string().optional(),
});

const UpdateCvDataSchema = z.object({
	id: z.number().int().positive(),
	name: z.string().min(1).optional(),
	data: CvTemplateSchema.optional().describe("CV data must match the default template structure"),
	is_public: z.boolean().optional(),
	source: z.string().optional(),
});

const GetCvDataSchema = z.object({
	id: z.number().int().positive(),
});

const GetCvDataByUserSchema = z.object({
	user_id: z.string(),
});

const ListCvDataSchema = z.object({
	user_id: z.string().optional(),
	is_public: z.boolean().optional(),
	source: z.string().optional(),
	search: z.string().optional(),
	limit: z.number().min(1).max(100).optional().default(50),
	offset: z.number().min(0).optional().default(0),
});

const UpdateCvDataDataSchema = z.object({
	id: z.number().int().positive(),
	data: CvTemplateSchema.describe("CV data must match the default template structure"),
});

// const DuplicateCvDataSchema = z.object({
// 	id: z.number().int().positive(),
// 	new_name: z.string().min(1).optional(),
// });

const ToggleCvVisibilitySchema = z.object({
	id: z.number().int().positive(),
});

// const GetCvDataBySourceSchema = z.object({
// 	source: z.string().min(1),
// });

export const addCvManagementTools = (server: McpServer): McpServer => {
	// Create CV Data Tool
	server.tool(
		"create_cv_data",
		"Creates a CV record from extracted document text. IMPORTANT: You must call `extract_document_data_tool` first to get the text content required by this tool.",
		{
			name: z.string().min(1).describe("Name/title for the CV data entry"),
			raw_data: z.string().describe("The raw text content of the CV. IMPORTANT: Get full text, don't truncate!"),
			data: CvTemplateSchema.describe(
				"CV data must match the default template structure with personal_info, about_me, key_skills, education, work_experiences, and projects sections",
			),
			is_public: z.boolean().optional().default(false).describe("Whether CV data is publicly visible"),
			source: z.string().optional().describe("Source of the CV data (e.g., 'manual', 'linkedin', 'upload')"),
		},
		async (args) => {
			try {
				const userId = await getCurrentUserId();
				console.log(`MCP Tool: create_cv_data called with user_id=${userId}, args=`, args);

				const validatedArgs = CreateCvDataSchema.parse(args);
				const result = await cvDataService.createCvData({ ...validatedArgs, user_id: userId });

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
					isError: false,
				};
			} catch (error) {
				console.error("MCP Tool: create_cv_data error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get CV Data Tool
	server.tool(
		"get_cv_data",
		"Retrieve detailed CV data information by ID including user details",
		{
			id: z.number().int().positive().describe("CV data ID to fetch"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: get_cv_data called with args:", args);

				const validatedArgs = GetCvDataSchema.parse(args);
				const cvData = await cvDataService.getCvDataById(validatedArgs.id);

				if (!cvData) {
					const result = {
						success: false,
						message: "CV data not found",
					};

					console.log("MCP Tool: get_cv_data result:", result);

					return {
						content: [
							{
								type: "text",
								text: JSON.stringify(result, null, 2),
							},
						],
					};
				}

				const result = {
					success: true,
					cv_data: cvData,
				};

				console.log("MCP Tool: get_cv_data result:", `Found CV data: ${cvData.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_cv_data error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get CV Data by User Tool
	server.tool(
		"get_cv_data_by_user",
		"Retrieve all CV data entries for a specific user",
		{
			user_id: z.string().describe("User id to fetch CV data for"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: get_cv_data_by_user called with args:", args);

				const validatedArgs = GetCvDataByUserSchema.parse(args);
				const cvDataList = await cvDataService.getCvDataByUser(validatedArgs.user_id);

				const result = {
					success: true,
					count: cvDataList.length,
					cv_data: cvDataList,
				};

				console.log("MCP Tool: get_cv_data_by_user result:", `Found ${cvDataList.length} CV entries for user`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: get_cv_data_by_user error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Update CV Data Tool
	server.tool(
		"update_cv_data",
		"Update existing CV data information such as name, data, visibility, or source",
		{
			id: z.number().int().positive().describe("CV data ID to update"),
			name: z.string().min(1).optional().describe("Updated name/title"),
			data: CvTemplateSchema.optional().describe("Updated CV data (must match the default template structure)"),
			is_public: z.boolean().optional().describe("Updated visibility setting"),
			source: z.string().optional().describe("Updated source information"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: update_cv_data called with args:", args);

				const validatedArgs = UpdateCvDataSchema.parse(args);
				const { id, ...updates } = validatedArgs;
				const cvData = await cvDataService.updateCvData(id, updates);

				const result = {
					success: true,
					message: "CV data updated successfully",
					cv_data: cvData,
				};

				console.log("MCP Tool: update_cv_data result:", `Updated CV data: ${cvData.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: update_cv_data error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// List CV Data Tool
	server.tool(
		"list_cv_data",
		"List CV data entries with optional filtering by user, visibility, source, or search criteria in name and data",
		{
			user_id: z.string().optional().describe("Filter by user id"),
			is_public: z.boolean().optional().describe("Filter by visibility (true for public, false for private)"),
			source: z.string().optional().describe("Filter by source"),
			search: z.string().optional().describe("Search in CV names and data"),
			limit: z.number().min(1).max(100).optional().default(50).describe("Limit number of results (1-100)"),
			offset: z.number().min(0).optional().default(0).describe("Offset for pagination"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: list_cv_data called with args:", args);

				const validatedArgs = ListCvDataSchema.parse(args);
				const { cvs, total } = await cvDataService.listCvData(validatedArgs);

				if (cvs.length === 0) {
					console.log("MCP Tool: list_cv_data result: No CVs found matching your criteria")
					return {
						content: [
							{
								type: "text",
								text: "No CVs found matching your criteria.",
							},
						],
					};
				}

				const result = {
					success: true,
					total,
					count: cvs.length,
					cv_data: cvs,
					pagination: {
						limit: validatedArgs.limit,
						offset: validatedArgs.offset,
						hasMore: validatedArgs.offset + cvs.length < total,
					},
				};

				console.log("MCP Tool: list_cv_data result:", `Found ${cvs.length} CV entries out of ${total} total`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: list_cv_data error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Delete CV Data Tool
	server.tool(
		"delete_cv_data",
		"Delete a CV data entry permanently",
		{
			id: z.number().int().positive().describe("CV data ID to delete"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: delete_cv_data called with args:", args);

				const { id } = z
					.object({
						id: z.number().int().positive(),
					})
					.parse(args);

				await cvDataService.deleteCvData(id);

				const result = {
					success: true,
					message: "CV data deleted successfully",
				};

				console.log("MCP Tool: delete_cv_data result:", `Deleted CV data with ID: ${id}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: delete_cv_data error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Update CV Data Data Tool
	server.tool(
		"update_cv_data_data",
		"Update only the data field of a CV entry (merge with existing data)",
		{
			id: z.number().int().positive().describe("CV data ID to update"),
			data: CvTemplateSchema.describe("Data updates (must match the default template structure)"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: update_cv_data_data called with args:", args);

				const validatedArgs = UpdateCvDataDataSchema.parse(args);
				const cvData = await cvDataService.updateCvDataData(validatedArgs.id, validatedArgs.data);

				const result = {
					success: true,
					message: "CV data updated successfully",
					cv_data: cvData,
				};

				console.log("MCP Tool: update_cv_data_data result:", `Updated data for CV: ${cvData.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: update_cv_data_data error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Duplicate CV Data Tool
	// server.tool(
	// 	"duplicate_cv_data",
	// 	"Create a duplicate copy of existing CV data with optional new name",
	// 	{
	// 		id: z.number().int().positive().describe("CV data ID to duplicate"),
	// 		new_name: z
	// 			.string()
	// 			.min(1)
	// 			.optional()
	// 			.describe("Name for the duplicated CV (defaults to 'Original Name (Copy)')"),
	// 	},
	// 	async (args) => {
	// 		try {
	// 			console.log("MCP Tool: duplicate_cv_data called with args:", args);

	// 			const validatedArgs = DuplicateCvDataSchema.parse(args);
	// 			const cvData = await cvDataService.duplicateCvData(validatedArgs.id, validatedArgs.new_name);

	// 			const result = {
	// 				success: true,
	// 				message: "CV data duplicated successfully",
	// 				cv_data: cvData,
	// 			};

	// 			console.log("MCP Tool: duplicate_cv_data result:", `Duplicated CV data: ${cvData.name}`);

	// 			return {
	// 				content: [
	// 					{
	// 						type: "text",
	// 						text: JSON.stringify(result, null, 2),
	// 					},
	// 				],
	// 			};
	// 		} catch (error) {
	// 			console.error("MCP Tool: duplicate_cv_data error:", error);

	// 			return {
	// 				content: [
	// 					{
	// 						type: "text",
	// 						text: JSON.stringify(
	// 							{
	// 								success: false,
	// 								error: error instanceof Error ? error.message : "Unknown error",
	// 							},
	// 							null,
	// 							2,
	// 						),
	// 					},
	// 				],
	// 				isError: true,
	// 			};
	// 		}
	// 	},
	// );

	// Toggle CV Visibility Tool
	server.tool(
		"toggle_cv_visibility",
		"Toggle the public/private visibility of a CV data entry",
		{
			id: z.number().int().positive().describe("CV data ID to toggle visibility for"),
		},
		async (args) => {
			try {
				console.log("MCP Tool: toggle_cv_visibility called with args:", args);

				const validatedArgs = ToggleCvVisibilitySchema.parse(args);
				const cvData = await cvDataService.toggleCvVisibility(validatedArgs.id);

				const result = {
					success: true,
					message: `CV data visibility toggled to ${cvData.is_public ? "public" : "private"}`,
					cv_data: cvData,
				};

				console.log("MCP Tool: toggle_cv_visibility result:", `Toggled visibility for CV: ${cvData.name}`);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(result, null, 2),
						},
					],
				};
			} catch (error) {
				console.error("MCP Tool: toggle_cv_visibility error:", error);

				return {
					content: [
						{
							type: "text",
							text: JSON.stringify(
								{
									success: false,
									error: error instanceof Error ? error.message : "Unknown error",
								},
								null,
								2,
							),
						},
					],
					isError: true,
				};
			}
		},
	);

	// Get Public CV Data Tool
	server.tool("get_public_cv_data", "Retrieve all publicly visible CV data entries", {}, async (args) => {
		try {
			console.log("MCP Tool: get_public_cv_data called");

			const cvDataList = await cvDataService.getPublicCvData();

			const result = {
				success: true,
				count: cvDataList.length,
				cv_data: cvDataList,
			};

			console.log("MCP Tool: get_public_cv_data result:", `Found ${cvDataList.length} public CV entries`);

			return {
				content: [
					{
						type: "text",
						text: JSON.stringify(result, null, 2),
					},
				],
			};
		} catch (error) {
			console.error("MCP Tool: get_public_cv_data error:", error);

			return {
				content: [
					{
						type: "text",
						text: JSON.stringify(
							{
								success: false,
								error: error instanceof Error ? error.message : "Unknown error",
							},
							null,
							2,
						),
					},
				],
				isError: true,
			};
		}
	});

	// // Get CV Data by Source Tool
	// server.tool(
	// 	"get_cv_data_by_source",
	// 	"Retrieve all CV data entries from a specific source",
	// 	{
	// 		source: z.string().min(1).describe("Source to filter by (e.g., 'manual', 'linkedin', 'upload')"),
	// 	},
	// 	async (args) => {
	// 		try {
	// 			console.log("MCP Tool: get_cv_data_by_source called with args:", args);

	// 			const validatedArgs = GetCvDataBySourceSchema.parse(args);
	// 			const cvDataList = await cvDataService.getCvDataBySource(validatedArgs.source);

	// 			const result = {
	// 				success: true,
	// 				count: cvDataList.length,
	// 				source: validatedArgs.source,
	// 				cv_data: cvDataList,
	// 			};

	// 			console.log(
	// 				"MCP Tool: get_cv_data_by_source result:",
	// 				`Found ${cvDataList.length} CV entries from source: ${validatedArgs.source}`,
	// 			);

	// 			return {
	// 				content: [
	// 					{
	// 						type: "text",
	// 						text: JSON.stringify(result, null, 2),
	// 					},
	// 				],
	// 			};
	// 		} catch (error) {
	// 			console.error("MCP Tool: get_cv_data_by_source error:", error);

	// 			return {
	// 				content: [
	// 					{
	// 						type: "text",
	// 						text: JSON.stringify(
	// 							{
	// 								success: false,
	// 								error: error instanceof Error ? error.message : "Unknown error",
	// 							},
	// 							null,
	// 							2,
	// 						),
	// 					},
	// 				],
	// 				isError: true,
	// 			};
	// 		}
	// 	},
	// );

	return server;
};
