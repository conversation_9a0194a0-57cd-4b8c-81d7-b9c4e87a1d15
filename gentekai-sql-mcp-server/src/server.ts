import { ContextAwareSSETransport } from "@/common/utils/ContextAwareSSETransport";
import cors from "cors";
import express, { type Express, type Request, type Response } from "express";
import helmet from "helmet";
import { pino } from "pino";

import { openAPIRouter } from "@/api-docs/openAPIRouter";
import { healthCheckRouter } from "@/services/healthCheck/healthCheckRouter";
import errorHandler from "@/common/middleware/errorHandler";
import requestLogger from "@/common/middleware/requestLogger";
import { env } from "@/common/utils/envConfig";
import rateLimiter from "./common/middleware/rateLimiter";
import { createMcpServer } from "./mcp/setupMcpServer";

const logger = pino({ name: "server start" });
const app: Express = express();

// Set the application to trust the reverse proxy
app.set("trust proxy", true);

// Middlewares
// app.use(express.json());
// app.use(express.urlencoded({ extended: true }));
app.use(cors({ origin: env.CORS_ORIGIN, credentials: true }));
app.use(helmet());
//app.use(rateLimiter);

// Request logging
app.use(requestLogger);

// Routes

const server = createMcpServer();

// to support multiple simultaneous connections we have a lookup object from
// sessionId to transport
const transports: { [sessionId: string]: ContextAwareSSETransport } = {};

app.get("/sse", async (_: Request, res: Response) => {
	const transport = new ContextAwareSSETransport("/messages", res);
	transports[transport.sessionId] = transport;
	console.log("New transport created for sessionId:", transport.sessionId);
	res.on("close", () => {
		delete transports[transport.sessionId];
	});
	await server.connect(transport);
});

app.post("/messages", async (req: Request, res: Response) => {
	const sessionId = req.query.sessionId as string;
	const transport = transports[sessionId];
	console.log("Received message for sessionId:", sessionId);

	if (transport) {
		await transport.handlePostMessage(req, res);
	} else {
		res.status(400).send("No transport found for sessionId");
	}
});

app.use(healthCheckRouter);

// Swagger UI. this contains / path as well so it should be last
app.use(openAPIRouter);

// Error handlers
app.use(errorHandler());

export { app, logger };
