import { AsyncLocalStorage } from 'async_hooks';
import type { Request, Response } from "express";
import { SSEServerTransport } from "@modelcontextprotocol/sdk/server/sse.js";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";

// Enhanced SSE Transport that sets user context
export class ContextAwareSSETransport extends SSEServerTransport {
	constructor(endpoint: string, response: Response) {
		super(endpoint, response);
	}

	async handlePostMessage(req: Request, res: Response): Promise<void> {
		const userIdHeader = req.headers['x-user-id'] || req.headers['X-User-Id'];
		const userId = Array.isArray(userIdHeader) ? userIdHeader[0] : userIdHeader;
		
		if (userId) {
			await asyncLocalStorage.run(
				{ sessionId: this.sessionId, userId: String(userId) },
				async () => {
					await super.handlePostMessage(req, res);
				}
			);
		} else {
			await super.handlePostMessage(req, res);
		}
	}
}

// AsyncLocalStorage for context management
export const asyncLocalStorage = new AsyncLocalStorage<{ sessionId: string; userId: string }>();

export const getCurrentUserId = (): string => {
    const context = asyncLocalStorage.getStore();
    if (!context) {
        throw new Error("No user context available. Please ensure X-User-Id header is provided.");
    }
    return context.userId;
};
